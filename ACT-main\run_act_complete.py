#!/usr/bin/env python3
"""
Complete ACT (Adaptive Activation Steering) Pipeline
This script runs the full ACT workflow: collect activations, generate directions, and validate
"""

import argparse
import subprocess
import sys
import os
import time

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    print(f"Running: {command}")
    print()
    
    start_time = time.time()
    result = subprocess.run(command, shell=True, capture_output=False)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"\n✓ {description} completed successfully in {end_time - start_time:.1f}s")
        return True
    else:
        print(f"\n✗ {description} failed with return code {result.returncode}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Run complete ACT pipeline")
    parser.add_argument('--model_name', type=str, default='gpt2', 
                       help='Model name (gpt2, gpt2-medium, etc.)')
    parser.add_argument('--max_samples', type=int, default=5,
                       help='Maximum number of samples for testing')
    parser.add_argument('--num_heads', type=int, default=12,
                       help='Number of attention heads (12 for GPT2)')
    parser.add_argument('--alpha', type=float, default=1.0,
                       help='Steering strength for validation')
    parser.add_argument('--skip_collect', action='store_true',
                       help='Skip activation collection (use existing files)')
    parser.add_argument('--skip_directions', action='store_true',
                       help='Skip direction generation (use existing files)')
    parser.add_argument('--skip_validation', action='store_true',
                       help='Skip validation step')
    
    args = parser.parse_args()
    
    print("🚀 ACT (Adaptive Activation Steering) Complete Pipeline")
    print("="*60)
    print(f"Model: {args.model_name}")
    print(f"Max samples: {args.max_samples}")
    print(f"Num heads: {args.num_heads}")
    print(f"Alpha: {args.alpha}")
    print()
    
    # Check if conda environment is activated
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'ACT':
        print("⚠️  Warning: ACT conda environment may not be activated")
        print("   Please run: conda activate ACT")
        print()
    
    success_count = 0
    total_steps = 3
    
    # Step 1: Collect Activations
    if not args.skip_collect:
        cmd = f"python collect_activations_fixed.py --model_name {args.model_name} --max_samples {args.max_samples}"
        if run_command(cmd, "Collecting Activations"):
            success_count += 1
        else:
            print("❌ Failed to collect activations. Stopping pipeline.")
            return
    else:
        print("\n⏭️  Skipping activation collection")
        success_count += 1
    
    # Step 2: Generate Directions
    if not args.skip_directions:
        cmd = f"python generate_directions_fixed.py --model_name {args.model_name} --num_heads {args.num_heads}"
        if run_command(cmd, "Generating Directions"):
            success_count += 1
        else:
            print("❌ Failed to generate directions. Stopping pipeline.")
            return
    else:
        print("\n⏭️  Skipping direction generation")
        success_count += 1
    
    # Step 3: Validate
    if not args.skip_validation:
        cmd = f"python validate_act_simplified.py --model_name {args.model_name} --alpha {args.alpha}"
        if run_command(cmd, "Validating ACT"):
            success_count += 1
        else:
            print("❌ Failed to validate ACT.")
            return
    else:
        print("\n⏭️  Skipping validation")
        success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("🎉 ACT PIPELINE COMPLETE!")
    print(f"{'='*60}")
    print(f"✅ Successfully completed {success_count}/{total_steps} steps")
    print()
    print("📁 Generated Files:")
    print(f"   - Activations: ./activations/{args.model_name}_head_wise.pkl")
    print(f"   - Labels: ./activations/{args.model_name}_labels.pkl")
    print(f"   - Directions: ./directions/{args.model_name}_directions.pkl")
    print(f"   - Results: ./validation/{args.model_name}_alpha{args.alpha}_simplified/")
    print()
    print("📊 Next Steps:")
    print("   1. Check the validation results in the validation directory")
    print("   2. Try different alpha values to optimize steering strength")
    print("   3. Test with larger models (if available)")
    print("   4. Implement the full TruthfulQA evaluation")
    print()
    print("🔧 For troubleshooting:")
    print("   - Check individual script outputs above")
    print("   - Verify all files were generated correctly")
    print("   - Try running steps individually if needed")

def run_quick_test():
    """Run a quick test with minimal samples"""
    print("🧪 Running Quick ACT Test...")
    args = argparse.Namespace(
        model_name='gpt2',
        max_samples=2,
        num_heads=12,
        alpha=1.0,
        skip_collect=False,
        skip_directions=False,
        skip_validation=False
    )
    
    # Simulate the main function with test args
    print("This would run the full pipeline with minimal samples for testing")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--quick-test':
        run_quick_test()
    else:
        main()
