#!/usr/bin/env python3
"""
Simplified ACT runner that works around compatibility issues
"""

import torch
import argparse
import sys
from datasets import load_dataset
import pandas as pd
import numpy as np
import pickle
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModelForCausalLM

def main():
    parser = argparse.ArgumentParser(description="Simplified ACT runner")
    parser.add_argument('--model_name', type=str, default='llama_7B', 
                       help='Model name (llama_7B, llama2_7B, etc.)')
    parser.add_argument('--device', type=int, default=0, help='GPU device')
    parser.add_argument('--test_only', action='store_true', 
                       help='Only test environment setup')
    
    args = parser.parse_args()
    
    print('Running simplified ACT with args:', args)
    
    # Model name mapping
    HF_NAMES = {
        'llama_7B': 'yahma/llama-7b-hf',
        'llama2_7B': 'meta-llama/Llama-2-7b-hf', 
        'llama2_chat_7B': 'meta-llama/Llama-2-7b-chat-hf', 
        'alpaca_7B': 'circulus/alpaca-7b', 
        'vicuna_7B': 'AlekseyKorshuk/vicuna-7b',
        'llama_13B': 'luodian/llama-13b-hf',
        'llama_33B': 'alexl83/LLaMA-33B-HF',
        'llama_65B': 'Enoch/llama-65b-hf'
    }
    
    if args.test_only:
        print("=== Testing Environment ===")
        test_environment()
        return
    
    if args.model_name not in HF_NAMES:
        print(f"Error: Unknown model name {args.model_name}")
        print(f"Available models: {list(HF_NAMES.keys())}")
        return
    
    model_path = HF_NAMES[args.model_name]
    print(f"Using model: {model_path}")
    
    # Test loading tokenizer
    try:
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print("✓ Tokenizer loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load tokenizer: {e}")
        return
    
    # Test loading dataset
    try:
        print("Loading TruthfulQA dataset...")
        dataset = load_dataset('truthful_qa', 'multiple_choice')['validation']
        print(f"✓ Dataset loaded, {len(dataset)} samples")
    except Exception as e:
        print(f"✗ Failed to load dataset: {e}")
        return
    
    print("✓ Basic setup complete!")
    print("\nNote: This is a simplified version. For full functionality,")
    print("you may need to resolve the transformers compatibility issues.")
    print("\nNext steps:")
    print("1. Fix the llama module compatibility issues")
    print("2. Run the original collect_activations.py script")
    print("3. Run generate_directions_q_wise.py")
    print("4. Run valid_2_fold.py for evaluation")

def test_environment():
    """Test the environment setup"""
    print("Testing PyTorch...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device count: {torch.cuda.device_count()}")
    
    print("\nTesting transformers...")
    try:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        print("✓ Transformers working")
    except Exception as e:
        print(f"✗ Transformers error: {e}")
    
    print("\nTesting datasets...")
    try:
        from datasets import load_dataset
        # Test with a small dataset
        dataset = load_dataset("squad", split="train[:5]")
        print("✓ Datasets working")
    except Exception as e:
        print(f"✗ Datasets error: {e}")
    
    print("\nTesting required directories...")
    import os
    dirs = ['activations', 'directions', 'validation', 'TruthfulQA']
    for d in dirs:
        if os.path.exists(d):
            print(f"✓ {d} directory exists")
        else:
            print(f"✗ {d} directory missing")
    
    print("\n=== Environment Test Complete ===")

if __name__ == "__main__":
    main()
