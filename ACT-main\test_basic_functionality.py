#!/usr/bin/env python3
"""
Basic functionality test for ACT environment
"""

def test_basic_imports():
    """Test basic imports without the problematic llama module"""
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
        
        import transformers
        print(f"✓ Transformers: {transformers.__version__}")
        
        import datasets
        print(f"✓ Datasets available")
        
        import pandas as pd
        print(f"✓ Pandas: {pd.__version__}")
        
        import numpy as np
        print(f"✓ NumPy: {np.__version__}")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_huggingface_model():
    """Test loading a simple HuggingFace model"""
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        # Test with a small model
        model_name = "gpt2"
        print(f"Testing with {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(f"✓ Tokenizer loaded")
        
        # Don't actually load the model to save time and memory
        print(f"✓ Model loading test passed")
        
        return True
    except Exception as e:
        print(f"✗ HuggingFace model test failed: {e}")
        return False

def test_truthfulqa_dataset():
    """Test loading TruthfulQA dataset"""
    try:
        from datasets import load_dataset
        
        print("Testing TruthfulQA dataset loading...")
        dataset = load_dataset('truthful_qa', 'multiple_choice', split='validation[:5]')
        print(f"✓ TruthfulQA dataset loaded, sample size: {len(dataset)}")
        
        return True
    except Exception as e:
        print(f"✗ TruthfulQA dataset test failed: {e}")
        return False

def test_collect_activations_imports():
    """Test imports needed for collect_activations.py"""
    try:
        # Test utils_simple import
        from utils_simple import tokenized_tqa_all
        print("✓ utils_simple.tokenized_tqa_all imported")
        
        # Test if we can import get_llama_activations_bau
        try:
            from utils_simple import get_llama_activations_bau
            print("✓ utils_simple.get_llama_activations_bau imported")
        except ImportError as e:
            print(f"⚠ utils_simple.get_llama_activations_bau import failed: {e}")
        
        return True
    except Exception as e:
        print(f"✗ collect_activations imports failed: {e}")
        return False

def main():
    print("=== ACT Basic Functionality Test ===\n")
    
    tests = [
        ("Basic imports", test_basic_imports),
        ("HuggingFace model", test_huggingface_model),
        ("TruthfulQA dataset", test_truthfulqa_dataset),
        ("Collect activations imports", test_collect_activations_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- Testing {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print(f"\n=== Test Results ===")
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All tests passed! Environment is ready.")
    else:
        print("⚠ Some tests failed. Environment may need additional setup.")

if __name__ == "__main__":
    main()
