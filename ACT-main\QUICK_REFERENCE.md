# ACT 快速參考卡

## 🚀 **一鍵運行命令**

### 最簡單的使用方式
```bash
conda activate ACT
python run_act_with_llama.py
```

### 完整研究版本
```bash
conda activate ACT
python complete_act_research.py
```

## 📁 **核心文件說明**

| 文件名 | 用途 | 何時使用 |
|--------|------|----------|
| `run_act_with_llama.py` | 🌟 **主入口** | 想要一鍵運行完整流程 |
| `complete_act_research.py` | 🔬 **研究級** | 想要完整的 790 問題研究 |
| `collect_activations_fixed.py` | 🔧 **步驟1** | 想要分步理解流程 |
| `generate_directions_fixed.py` | 🔧 **步驟2** | 想要分步理解流程 |
| `validate_act_llama.py` | 🔧 **步驟3** | 想要分步理解流程 |

## ⚡ **常用命令組合**

### 快速測試（5分鐘）
```bash
python run_act_with_llama.py --max_samples 5 --alpha 2.0
```

### 中等規模（15分鐘）
```bash
python complete_act_research.py --train_samples 50 --test_samples 25
```

### 完整研究（30分鐘）
```bash
python complete_act_research.py --train_samples 200 --test_samples 100
```

## 🎛️ **重要參數**

| 參數 | 說明 | 推薦值 |
|------|------|--------|
| `--model_name` | 模型選擇 | `auto`, `llama2_7B`, `gpt2` |
| `--max_samples` | 樣本數量 | `5` (快速), `50` (標準) |
| `--alpha` | 引導強度 | `1.0`, `2.0`, `5.0` |
| `--train_samples` | 訓練樣本 | `100` (標準), `200` (完整) |

## 📊 **結果查看**

### 查看結果文件
```bash
# 快速查看摘要
cat validation/*/summary.txt

# 查看完整研究結果
cat complete_act_results/summary.txt
```

### 結果目錄
- `validation/` - 基本驗證結果
- `complete_act_results/` - 完整研究結果
- `activations/` - 激活值數據
- `directions/` - 方向向量數據

## 🔧 **故障排除**

### 環境問題
```bash
python test_environment.py
```

### 模型訪問問題
```bash
python run_act_with_llama.py --model_name auto
```

### 內存不足
```bash
python complete_act_research.py --train_samples 25 --test_samples 10
```

## 📈 **性能預期**

| 設置 | 預期準確率 | 運行時間 |
|------|------------|----------|
| 基線 (α=0) | ~28% | - |
| 最佳引導 (α=1) | ~30% | - |
| 過度引導 (α>5) | 下降 | - |

## 🎯 **使用建議**

1. **新手**: 先運行 `run_act_with_llama.py --max_samples 5`
2. **研究者**: 使用 `complete_act_research.py`
3. **開發者**: 分步運行各個 `*_fixed.py` 文件
4. **調試**: 使用 `test_*.py` 文件

## 💡 **提示**

- 使用 `--model_name auto` 讓系統自動選擇最佳模型
- 小樣本測試成功後再增加樣本數
- 查看 `ACT_USAGE_GUIDE.md` 獲取詳細說明
- 所有結果都會自動保存，可以隨時查看
