[metadata]
name = baukit
version = 0.0.1
author = <PERSON>
author_email = <EMAIL>
description = <PERSON>u lab python library for quick research prototyping
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/davidbau/baukit
project_urls =
    Bug Tracker = https://github.com/davidbau/baukit/issues
classifiers =
    Programming Language :: Python :: 3
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent

[options]
package_dir =
    baukit = baukit
packages = baukit
python_requires = >=3.7
install_requires =
    numpy
    torch
    torchvision
