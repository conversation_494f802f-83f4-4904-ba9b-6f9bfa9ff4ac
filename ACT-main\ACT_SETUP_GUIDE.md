# ACT 環境設置指南

## 概述
這份指南將幫助您設置並運行 ACT (Adaptive Activation Steering) 專案。ACT 是一個用於改進大型語言模型真實性的無需調優方法。

## 環境狀態
✅ **已完成的設置：**
- Conda 環境 `ACT` 已創建並配置
- 基本依賴已安裝（PyTorch, Transformers, Datasets 等）
- 必要目錄已創建（activations, directions, validation）
- TruthfulQA 數據集已準備
- CUDA 支持已啟用

⚠️ **已知問題：**
- LLaMA 模組存在 transformers 版本兼容性問題
- 某些依賴版本衝突（但不影響基本功能）

## 快速開始

### 1. 激活環境
```bash
conda activate ACT
```

### 2. 測試環境
```bash
python run_act_simplified.py --test_only
```

### 3. 測試基本功能
```bash
python test_basic_functionality.py
```

## 主要腳本說明

### 原始工作流程
根據 README.md，完整的工作流程包括：

1. **收集激活值：**
   ```bash
   python collect_activations.py --model_name llama_7B --device 0
   ```

2. **生成方向向量：**
   ```bash
   python generate_directions_q_wise.py --model_name llama_7B
   ```

3. **驗證評估：**
   ```bash
   python valid_2_fold.py --model_name llama_7B --num_heads 24 --alpha 12 --n_clusters 3 --probe_base_weight 0 --judge_name <your GPT-judge name> --info_name <your GPT-info name>
   ```

### 當前狀態
由於 transformers 版本兼容性問題，原始腳本可能無法直接運行。我們提供了以下替代方案：

- `run_act_simplified.py`: 簡化版本，用於測試環境和基本功能
- `test_basic_functionality.py`: 環境測試腳本
- `test_environment.py`: 詳細的環境驗證

## 支持的模型

腳本支持以下模型：
- `llama_7B`: yahma/llama-7b-hf
- `llama2_7B`: meta-llama/Llama-2-7b-hf
- `llama2_chat_7B`: meta-llama/Llama-2-7b-chat-hf
- `alpaca_7B`: circulus/alpaca-7b
- `vicuna_7B`: AlekseyKorshuk/vicuna-7b
- `llama_13B`: luodian/llama-13b-hf
- `llama_33B`: alexl83/LLaMA-33B-HF
- `llama_65B`: Enoch/llama-65b-hf

## 故障排除

### 1. NumPy 版本問題
如果遇到 NumPy 兼容性錯誤：
```bash
conda run -n ACT pip install numpy==1.24.3
```

### 2. Transformers 兼容性問題
當前安裝的是較舊版本的 transformers (4.6.0.dev0)，這可能導致某些功能不可用。

### 3. CUDA 兼容性警告
您的 RTX 5070 Ti 顯卡可能與當前 PyTorch 版本不完全兼容，但基本功能仍可正常使用。

## 系統要求

- **操作系統：** Windows (已測試)
- **Python：** 3.8+ (當前使用 3.12.7)
- **GPU：** NVIDIA GPU with CUDA support (已檢測到 RTX 5070 Ti)
- **內存：** 建議 16GB+ RAM
- **存儲：** 至少 10GB 可用空間

## 下一步

1. **解決兼容性問題：** 更新 transformers 版本或修復 llama 模組
2. **運行完整流程：** 一旦兼容性問題解決，可以運行完整的 ACT 流程
3. **設置 OpenAI API：** 如需使用 TruthfulQA 評估，需要設置 OpenAI API 密鑰

## 聯繫支持

如果遇到問題，請檢查：
1. 環境是否正確激活
2. 所有依賴是否已安裝
3. CUDA 是否可用
4. 必要的目錄和文件是否存在

## 更新日誌

- **2025-01-01:** 初始環境設置完成
- **2025-01-01:** 修復 NumPy 兼容性問題
- **2025-01-01:** 創建簡化測試腳本
