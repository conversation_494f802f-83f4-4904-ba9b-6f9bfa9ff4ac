@echo off
echo ========================================
echo    ACT Environment Activation Script
echo ========================================
echo.

echo Activating ACT conda environment...
call conda activate ACT

echo.
echo Environment activated! You can now run:
echo   python run_act_simplified.py --test_only
echo   python test_basic_functionality.py
echo   python collect_activations.py --help
echo.

echo Current environment info:
python --version
echo.

echo CUDA availability:
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
echo.

echo ========================================
echo Ready to use ACT! Type 'exit' to leave.
echo ========================================

cmd /k
