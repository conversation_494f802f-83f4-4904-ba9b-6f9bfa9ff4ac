# ACT (Activation Steering) 完整使用指南

## 📋 目錄
1. [快速開始](#快速開始)
2. [代碼文件說明](#代碼文件說明)
3. [使用場景](#使用場景)
4. [詳細教程](#詳細教程)
5. [故障排除](#故障排除)

## 🚀 快速開始

### 最簡單的使用方式
```bash
# 1. 激活環境
conda activate ACT

# 2. 運行完整研究（推薦）
python complete_act_research.py --model_name llama2_7B --train_samples 50 --test_samples 30

# 3. 查看結果
cat complete_act_results/summary.txt
```

## 📁 代碼文件說明

### 🎯 **核心文件（必須了解）**

#### 1. `complete_act_research.py` ⭐⭐⭐⭐⭐
**最重要的文件 - 完整研究實現**
- **用途**: 運行完整的 ACT 研究流程
- **包含**: 數據加載、激活提取、方向計算、評估
- **推薦**: 新手和研究使用

```bash
# 基本使用
python complete_act_research.py

# 自定義參數
python complete_act_research.py \
    --model_name llama2_7B \
    --train_samples 100 \
    --test_samples 50 \
    --alpha_values 0.0 1.0 2.0 5.0 \
    --save_activations
```

#### 2. `run_act_with_llama.py` ⭐⭐⭐⭐
**智能模型選擇和完整流程**
- **用途**: 自動選擇最佳可用模型並運行 ACT
- **特點**: 自動回退、錯誤處理
- **推薦**: 不確定模型可用性時使用

```bash
# 自動選擇模型
python run_act_with_llama.py --model_name auto

# 指定模型
python run_act_with_llama.py --model_name llama2_7B --max_samples 20
```

### 🔧 **功能文件（按需使用）**

#### 3. `collect_activations_llama.py` ⭐⭐⭐
**LLaMA 模型激活提取**
- **用途**: 專門為 LLaMA 模型提取激活值
- **何時使用**: 需要單獨提取激活值時

```bash
python collect_activations_llama.py --model_name llama2_7B --max_samples 20
```

#### 4. `validate_act_llama.py` ⭐⭐⭐
**LLaMA 模型驗證**
- **用途**: 使用已有的方向向量驗證 ACT 效果
- **何時使用**: 已有方向向量，只需要測試不同 alpha 值

```bash
python validate_act_llama.py --model_name llama2_7B --alpha 2.0
```

#### 5. `act_with_real_data.py` ⭐⭐⭐
**真實數據基線測試**
- **用途**: 測試模型在真實 TruthfulQA 數據上的基線性能
- **何時使用**: 想了解模型原始性能時

```bash
python act_with_real_data.py --model_name llama2_7B --num_samples 30
```

### 📊 **分析文件（了解即可）**

#### 6. `analyze_real_truthfulqa.py` ⭐⭐
**數據集分析**
- **用途**: 分析 TruthfulQA 數據集結構
- **何時使用**: 想了解數據集詳情時

#### 7. `large_scale_act_test.py` ⭐⭐
**大規模測試**
- **用途**: 使用更多問題進行測試
- **何時使用**: 需要更大規模驗證時

### 🛠️ **工具文件（輔助功能）**

#### 8. `setup_hf_auth.py` ⭐
**認證設置**
- **用途**: 設置 Hugging Face 認證
- **何時使用**: 首次使用或認證問題時

#### 9. `test_*.py` 文件 ⭐
**環境測試**
- **用途**: 測試環境是否正確設置
- **何時使用**: 環境問題排查時

### 📋 **配置文件**

#### 10. `ACT_SETUP_GUIDE.md`
**設置指南**

#### 11. `activate_act.bat`
**Windows 激活腳本**

## 🎯 使用場景

### 場景 1: 第一次使用 ACT
```bash
# 1. 測試環境
python test_environment.py

# 2. 運行完整研究（小規模）
python complete_act_research.py --train_samples 20 --test_samples 10

# 3. 查看結果
cat complete_act_results/summary.txt
```

### 場景 2: 研究不同模型
```bash
# 測試 LLaMA-2-7B
python complete_act_research.py --model_name llama2_7B

# 測試 LLaMA-2-Chat
python complete_act_research.py --model_name llama2_chat_7B
```

### 場景 3: 優化 Alpha 參數
```bash
# 測試更多 alpha 值
python complete_act_research.py \
    --alpha_values 0.0 0.5 1.0 1.5 2.0 3.0 5.0 \
    --train_samples 50 \
    --test_samples 30
```

### 場景 4: 大規模研究
```bash
# 使用更多數據
python complete_act_research.py \
    --train_samples 200 \
    --test_samples 100 \
    --save_activations
```

### 場景 5: 快速測試
```bash
# 自動選擇模型，快速測試
python run_act_with_llama.py --model_name auto --max_samples 10
```

## 📖 詳細教程

### 教程 1: 完整研究流程

1. **準備環境**
```bash
conda activate ACT
```

2. **運行完整研究**
```bash
python complete_act_research.py \
    --model_name llama2_7B \
    --train_samples 100 \
    --test_samples 50 \
    --alpha_values 0.0 1.0 2.0 5.0 \
    --save_activations
```

3. **分析結果**
```bash
# 查看摘要
cat complete_act_results/summary.txt

# 查看詳細結果
python -c "
import pickle
with open('complete_act_results/complete_results.pkl', 'rb') as f:
    results = pickle.load(f)
print('Best Alpha:', results['best_alpha'])
print('Improvement:', results['improvement'])
"
```

### 教程 2: 自定義實驗

1. **修改訓練數據**
```python
# 編輯 complete_act_research.py
# 修改第 X 行的訓練樣本數量
args.train_samples = 200  # 增加到 200
```

2. **添加新的 Alpha 值**
```bash
python complete_act_research.py --alpha_values 0.0 0.1 0.5 1.0 2.0 3.0 5.0 10.0
```

3. **保存中間結果**
```bash
python complete_act_research.py --save_activations
```

## 🔧 故障排除

### 問題 1: 模型加載失敗
```bash
# 解決方案：使用自動選擇
python run_act_with_llama.py --model_name auto
```

### 問題 2: 內存不足
```bash
# 解決方案：減少樣本數量
python complete_act_research.py --train_samples 20 --test_samples 10
```

### 問題 3: CUDA 兼容性警告
```bash
# 解決方案：忽略警告，或使用 CPU
# 警告不影響功能，可以正常使用
```

### 問題 4: 認證問題
```bash
# 解決方案：重新設置認證
python setup_hf_auth.py
```

## 📊 結果解讀

### 輸出文件說明
```
complete_act_results/
├── activations.pkl      # 提取的激活值
├── directions.pkl       # 計算的方向向量
├── complete_results.pkl # 完整實驗結果
└── summary.txt         # 結果摘要（人類可讀）
```

### 關鍵指標
- **Baseline Accuracy**: 無引導時的準確率
- **Best Alpha**: 最佳引導強度
- **Improvement**: 改進幅度
- **Relative Improvement**: 相對改進百分比

## 🎯 推薦工作流程

### 新手推薦
1. `test_environment.py` - 測試環境
2. `complete_act_research.py` (小規模) - 快速驗證
3. `complete_act_research.py` (大規模) - 完整研究

### 研究者推薦
1. `analyze_real_truthfulqa.py` - 了解數據
2. `complete_act_research.py` - 完整實驗
3. 自定義參數重複實驗

### 開發者推薦
1. 閱讀 `complete_act_research.py` 源碼
2. 修改和擴展功能
3. 使用組件文件進行定制開發

## 📞 獲取幫助

如果遇到問題：
1. 查看 `ACT_SETUP_GUIDE.md`
2. 運行 `test_environment.py`
3. 檢查錯誤日誌
4. 嘗試減少樣本數量
