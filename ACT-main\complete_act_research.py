#!/usr/bin/env python3
"""
Complete ACT Research Implementation
Based on "Inference-Time Intervention: Eliciting Truthful Answers from a Language Model"

This implements the full research pipeline:
1. Load complete TruthfulQA dataset (790 questions)
2. Extract activations for truthful vs untruthful responses
3. Compute truthfulness direction vectors
4. Apply activation steering during inference
5. Evaluate on held-out test set
"""

import torch
import numpy as np
import argparse
import pickle as pkl
import pandas as pd
import sys
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm
import json
from typing import List, Dict, Tuple, Optional
import logging
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set HF token
os.environ['HUGGINGFACE_HUB_TOKEN'] = '*************************************'

class CompleteTruthfulQADataset:
    """Complete TruthfulQA dataset handler"""
    
    def __init__(self, csv_path='./TruthfulQA/TruthfulQA.csv'):
        self.csv_path = csv_path
        self.data = None
        self.train_data = None
        self.test_data = None
        
    def load_data(self):
        """Load the complete TruthfulQA dataset"""
        logger.info("Loading complete TruthfulQA dataset...")
        
        df = pd.read_csv(self.csv_path)
        logger.info(f"Loaded {len(df)} questions from TruthfulQA")
        
        # Convert to our format
        processed_data = []
        
        for idx, row in df.iterrows():
            try:
                question = row['Question']
                best_answer = row['Best Answer']
                
                # Parse correct and incorrect answers
                correct_answers = row['Correct Answers'].split('; ')
                incorrect_answers = row['Incorrect Answers'].split('; ')
                
                # Clean and filter answers
                correct_answers = [ans.strip() for ans in correct_answers if len(ans.strip()) > 0]
                incorrect_answers = [ans.strip() for ans in incorrect_answers if len(ans.strip()) > 0 and len(ans.strip()) < 200]
                
                processed_data.append({
                    'question': question,
                    'best_answer': best_answer,
                    'correct_answers': correct_answers,
                    'incorrect_answers': incorrect_answers,
                    'category': row['Category'],
                    'type': row['Type']
                })
                
            except Exception as e:
                logger.warning(f"Skipping question {idx}: {e}")
                continue
        
        self.data = processed_data
        logger.info(f"Successfully processed {len(self.data)} questions")
        
        return self.data
    
    def create_train_test_split(self, test_size=0.3, random_state=42):
        """Split data into train and test sets"""
        if self.data is None:
            self.load_data()
        
        self.train_data, self.test_data = train_test_split(
            self.data, test_size=test_size, random_state=random_state, 
            stratify=[item['category'] for item in self.data]
        )
        
        logger.info(f"Train set: {len(self.train_data)} questions")
        logger.info(f"Test set: {len(self.test_data)} questions")
        
        return self.train_data, self.test_data

class ACTActivationExtractor:
    """Extract activations for truthful vs untruthful responses"""
    
    def __init__(self, model, tokenizer, device):
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        
    def extract_training_activations(self, train_data, max_samples=None):
        """Extract activations from training data"""
        
        if max_samples:
            train_data = train_data[:max_samples]
        
        truthful_activations = []
        untruthful_activations = []
        
        logger.info(f"Extracting activations from {len(train_data)} training questions...")
        
        with torch.no_grad():
            for item in tqdm(train_data, desc="Extracting training activations"):
                question = item['question']
                correct_answers = item['correct_answers']
                incorrect_answers = item['incorrect_answers']
                
                # Extract truthful activations
                for correct_answer in correct_answers[:2]:  # Use first 2 correct answers
                    activation = self._get_activation(question, correct_answer)
                    if activation is not None:
                        truthful_activations.append(activation)
                
                # Extract untruthful activations
                for incorrect_answer in incorrect_answers[:3]:  # Use first 3 incorrect answers
                    activation = self._get_activation(question, incorrect_answer)
                    if activation is not None:
                        untruthful_activations.append(activation)
        
        truthful_activations = np.array(truthful_activations)
        untruthful_activations = np.array(untruthful_activations)
        
        logger.info(f"Extracted {len(truthful_activations)} truthful activations")
        logger.info(f"Extracted {len(untruthful_activations)} untruthful activations")
        
        return truthful_activations, untruthful_activations
    
    def _get_activation(self, question, answer):
        """Get activation for a question-answer pair"""
        try:
            prompt = f"Question: {question}\nAnswer: {answer}"
            
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            outputs = self.model(**inputs, output_hidden_states=True)
            
            # Extract from multiple layers
            hidden_states = outputs.hidden_states
            
            # Use last token from last layer
            last_layer_activation = hidden_states[-1][0, -1, :].cpu().numpy()
            
            return last_layer_activation
            
        except Exception as e:
            logger.warning(f"Failed to extract activation: {e}")
            return None

class ACTDirectionComputer:
    """Compute truthfulness direction vectors"""
    
    def __init__(self):
        self.directions = {}
        
    def compute_directions(self, truthful_activations, untruthful_activations, method='mean_diff'):
        """Compute truthfulness direction vectors"""
        
        logger.info(f"Computing directions using method: {method}")
        
        if method == 'mean_diff':
            # Simple mean difference
            truthful_mean = np.mean(truthful_activations, axis=0)
            untruthful_mean = np.mean(untruthful_activations, axis=0)
            direction = truthful_mean - untruthful_mean
            
        elif method == 'pca':
            # PCA-based direction
            all_activations = np.vstack([truthful_activations, untruthful_activations])
            labels = np.hstack([np.ones(len(truthful_activations)), np.zeros(len(untruthful_activations))])
            
            pca = PCA(n_components=1)
            pca.fit(all_activations)
            direction = pca.components_[0]
            
        else:
            raise ValueError(f"Unknown method: {method}")
        
        # Normalize direction
        direction = direction / np.linalg.norm(direction)
        
        self.directions['main'] = direction
        
        logger.info(f"Computed direction vector with shape: {direction.shape}")
        logger.info(f"Direction norm: {np.linalg.norm(direction):.6f}")
        
        return direction

class ACTInferenceEngine:
    """Apply activation steering during inference"""
    
    def __init__(self, model, tokenizer, directions):
        self.model = model
        self.tokenizer = tokenizer
        self.directions = directions
        self.hooks = []
        
    def register_steering_hooks(self, alpha, target_layers=None):
        """Register hooks for activation steering"""
        
        if target_layers is None:
            # Target middle layers
            num_layers = len(self.model.model.layers)
            target_layers = list(range(num_layers // 4, 3 * num_layers // 4))
        
        logger.info(f"Registering steering hooks on {len(target_layers)} layers with α={alpha}")
        
        direction = torch.tensor(self.directions['main'], dtype=torch.float16, device=self.model.device)
        
        def steering_hook(module, input, output):
            if isinstance(output, tuple):
                hidden_states = output[0]
            else:
                hidden_states = output
            
            # Apply steering to last token
            hidden_states[:, -1, :] += alpha * direction
            
            if isinstance(output, tuple):
                return (hidden_states,) + output[1:]
            else:
                return hidden_states
        
        for layer_idx in target_layers:
            if layer_idx < len(self.model.model.layers):
                layer = self.model.model.layers[layer_idx]
                handle = layer.register_forward_hook(steering_hook)
                self.hooks.append(handle)
    
    def remove_hooks(self):
        """Remove all steering hooks"""
        for handle in self.hooks:
            handle.remove()
        self.hooks = []
    
    def evaluate_with_steering(self, test_data, alpha_values, max_samples=None):
        """Evaluate with different steering strengths"""
        
        if max_samples:
            test_data = test_data[:max_samples]
        
        results = {}
        
        for alpha in alpha_values:
            logger.info(f"Evaluating with α={alpha}")
            
            # Register hooks
            self.register_steering_hooks(alpha)
            
            try:
                accuracy, category_results = self._evaluate_accuracy(test_data)
                results[alpha] = {
                    'accuracy': accuracy,
                    'category_results': category_results
                }
                
                logger.info(f"α={alpha}: Accuracy = {accuracy:.3f}")
                
            finally:
                self.remove_hooks()
        
        return results
    
    def _evaluate_accuracy(self, test_data):
        """Evaluate accuracy on test data"""
        
        correct = 0
        total = 0
        category_results = {}
        
        with torch.no_grad():
            for item in tqdm(test_data, desc="Evaluating", leave=False):
                question = item['question']
                correct_answers = item['correct_answers']
                incorrect_answers = item['incorrect_answers']
                category = item['category']
                
                # Create multiple choice question
                choices = [correct_answers[0]]  # First correct answer
                choices.extend(incorrect_answers[:3])  # First 3 incorrect answers
                
                if len(choices) < 4:
                    choices.extend(['I don\'t know'] * (4 - len(choices)))
                
                # Evaluate choices
                choice_scores = []
                for choice in choices:
                    score = self._score_answer(question, choice)
                    choice_scores.append(score)
                
                predicted_idx = np.argmax(choice_scores)
                is_correct = predicted_idx == 0  # First choice is correct
                
                if is_correct:
                    correct += 1
                total += 1
                
                # Track by category
                if category not in category_results:
                    category_results[category] = {'correct': 0, 'total': 0}
                category_results[category]['total'] += 1
                if is_correct:
                    category_results[category]['correct'] += 1
        
        accuracy = correct / total if total > 0 else 0
        return accuracy, category_results
    
    def _score_answer(self, question, answer):
        """Score a question-answer pair"""
        prompt = f"Question: {question}\nAnswer: {answer}"
        
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
        
        outputs = self.model(**inputs)
        
        # Simple scoring: mean of last token logits
        score = outputs.logits[0, -1, :].mean().item()
        return score

def main():
    parser = argparse.ArgumentParser(description="Complete ACT Research Implementation")
    parser.add_argument('--model_name', type=str, default='llama2_7B')
    parser.add_argument('--train_samples', type=int, default=200, help='Training samples for direction learning')
    parser.add_argument('--test_samples', type=int, default=100, help='Test samples for evaluation')
    parser.add_argument('--alpha_values', nargs='+', type=float, default=[0.0, 1.0, 2.0, 5.0, 10.0])
    parser.add_argument('--output_dir', type=str, default='./complete_act_results')
    parser.add_argument('--save_activations', action='store_true', help='Save extracted activations')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    logger.info("🔬 Starting Complete ACT Research Implementation")
    logger.info(f"Model: {args.model_name}")
    logger.info(f"Training samples: {args.train_samples}")
    logger.info(f"Test samples: {args.test_samples}")
    logger.info(f"Alpha values: {args.alpha_values}")
    
    # Model mapping
    HF_NAMES = {
        'llama2_7B': 'meta-llama/Llama-2-7b-hf',
        'llama2_chat_7B': 'meta-llama/Llama-2-7b-chat-hf',
    }
    
    # Load model
    logger.info("Loading model...")
    model_path = HF_NAMES[args.model_name]
    tokenizer = AutoTokenizer.from_pretrained(model_path, token=os.environ.get('HUGGINGFACE_HUB_TOKEN'))
    tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16,
        device_map='auto',
        token=os.environ.get('HUGGINGFACE_HUB_TOKEN')
    )
    device = next(model.parameters()).device
    
    # Load dataset
    logger.info("Loading complete TruthfulQA dataset...")
    dataset = CompleteTruthfulQADataset()
    train_data, test_data = dataset.create_train_test_split()
    
    logger.info(f"Dataset loaded: {len(dataset.data)} total questions")
    logger.info(f"Train: {len(train_data)}, Test: {len(test_data)}")
    
    # Extract activations
    logger.info("Extracting activations...")
    extractor = ACTActivationExtractor(model, tokenizer, device)
    truthful_activations, untruthful_activations = extractor.extract_training_activations(
        train_data, max_samples=args.train_samples
    )
    
    if args.save_activations:
        activations_file = os.path.join(args.output_dir, 'activations.pkl')
        with open(activations_file, 'wb') as f:
            pkl.dump({
                'truthful': truthful_activations,
                'untruthful': untruthful_activations
            }, f)
        logger.info(f"Activations saved to {activations_file}")
    
    # Compute directions
    logger.info("Computing truthfulness directions...")
    direction_computer = ACTDirectionComputer()
    direction = direction_computer.compute_directions(truthful_activations, untruthful_activations)
    
    # Save directions
    directions_file = os.path.join(args.output_dir, 'directions.pkl')
    with open(directions_file, 'wb') as f:
        pkl.dump(direction_computer.directions, f)
    logger.info(f"Directions saved to {directions_file}")
    
    # Evaluate with steering
    logger.info("Evaluating with activation steering...")
    inference_engine = ACTInferenceEngine(model, tokenizer, direction_computer.directions)
    results = inference_engine.evaluate_with_steering(
        test_data, args.alpha_values, max_samples=args.test_samples
    )
    
    # Print results
    logger.info("\n" + "="*60)
    logger.info("COMPLETE ACT RESEARCH RESULTS")
    logger.info("="*60)
    
    for alpha, result in results.items():
        logger.info(f"α={alpha:4.1f}: Accuracy = {result['accuracy']:.3f}")
    
    # Find best alpha
    best_alpha = max(results.keys(), key=lambda a: results[a]['accuracy'])
    best_accuracy = results[best_alpha]['accuracy']
    baseline_accuracy = results[0.0]['accuracy']
    improvement = best_accuracy - baseline_accuracy
    
    logger.info(f"\nBest α: {best_alpha} (Accuracy: {best_accuracy:.3f})")
    logger.info(f"Baseline: {baseline_accuracy:.3f}")
    logger.info(f"Improvement: {improvement:.3f} ({improvement/baseline_accuracy*100:.1f}%)")
    
    # Save complete results
    final_results = {
        'model_name': args.model_name,
        'train_samples': args.train_samples,
        'test_samples': args.test_samples,
        'alpha_values': args.alpha_values,
        'results': results,
        'best_alpha': best_alpha,
        'best_accuracy': best_accuracy,
        'baseline_accuracy': baseline_accuracy,
        'improvement': improvement
    }
    
    results_file = os.path.join(args.output_dir, 'complete_results.pkl')
    with open(results_file, 'wb') as f:
        pkl.dump(final_results, f)
    
    # Save summary
    summary_file = os.path.join(args.output_dir, 'summary.txt')
    with open(summary_file, 'w') as f:
        f.write("Complete ACT Research Results\n")
        f.write("="*50 + "\n")
        f.write(f"Model: {args.model_name}\n")
        f.write(f"Training samples: {args.train_samples}\n")
        f.write(f"Test samples: {args.test_samples}\n\n")
        f.write("Results by Alpha:\n")
        for alpha, result in results.items():
            f.write(f"  α={alpha:4.1f}: {result['accuracy']:.3f}\n")
        f.write(f"\nBest α: {best_alpha} (Accuracy: {best_accuracy:.3f})\n")
        f.write(f"Baseline: {baseline_accuracy:.3f}\n")
        f.write(f"Improvement: {improvement:.3f} ({improvement/baseline_accuracy*100:.1f}%)\n")
    
    logger.info(f"\nResults saved to {args.output_dir}")
    logger.info("🎉 Complete ACT research implementation finished!")

if __name__ == '__main__':
    main()
