#!/usr/bin/env python3
"""
ACT Quick Start Script
一鍵運行 ACT 研究的最簡單方式
"""

import argparse
import subprocess
import sys
import os
import time

def print_banner():
    """打印歡迎橫幅"""
    print("🚀" + "="*60 + "🚀")
    print("    ACT (Activation Steering) Quick Start")
    print("    一鍵運行完整的真實性改進研究")
    print("🚀" + "="*60 + "🚀")
    print()

def check_environment():
    """檢查環境是否正確"""
    print("🔍 檢查環境...")
    
    # 檢查 conda 環境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'ACT':
        print("⚠️  警告: 請先激活 ACT 環境")
        print("   運行: conda activate ACT")
        return False
    
    # 檢查必要文件
    required_files = [
        'complete_act_research.py',
        'TruthfulQA/TruthfulQA.csv'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少文件: {file}")
            return False
    
    print("✅ 環境檢查通過")
    return True

def run_quick_test():
    """運行快速測試"""
    print("\n🧪 運行快速測試 (5分鐘)...")
    
    cmd = [
        "python", "complete_act_research.py",
        "--model_name", "llama2_7B",
        "--train_samples", "20",
        "--test_samples", "10",
        "--alpha_values", "0.0", "1.0", "2.0"
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=False)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"\n✅ 快速測試完成 ({end_time - start_time:.1f}秒)")
        return True
    else:
        print(f"\n❌ 快速測試失敗")
        return False

def run_standard_research():
    """運行標準研究"""
    print("\n🔬 運行標準研究 (15-20分鐘)...")
    
    cmd = [
        "python", "complete_act_research.py",
        "--model_name", "llama2_7B",
        "--train_samples", "100",
        "--test_samples", "50",
        "--alpha_values", "0.0", "1.0", "2.0", "5.0",
        "--save_activations"
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=False)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"\n✅ 標準研究完成 ({end_time - start_time:.1f}秒)")
        return True
    else:
        print(f"\n❌ 標準研究失敗")
        return False

def run_full_research():
    """運行完整研究"""
    print("\n🏆 運行完整研究 (30-45分鐘)...")
    
    cmd = [
        "python", "complete_act_research.py",
        "--model_name", "llama2_7B",
        "--train_samples", "200",
        "--test_samples", "100",
        "--alpha_values", "0.0", "0.5", "1.0", "1.5", "2.0", "3.0", "5.0",
        "--save_activations"
    ]
    
    print(f"執行命令: {' '.join(cmd)}")
    
    start_time = time.time()
    result = subprocess.run(cmd, capture_output=False)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"\n✅ 完整研究完成 ({end_time - start_time:.1f}秒)")
        return True
    else:
        print(f"\n❌ 完整研究失敗")
        return False

def show_results():
    """顯示結果"""
    print("\n📊 查看結果...")
    
    summary_file = "complete_act_results/summary.txt"
    if os.path.exists(summary_file):
        print("\n" + "="*50)
        print("📋 研究結果摘要:")
        print("="*50)
        with open(summary_file, 'r') as f:
            print(f.read())
    else:
        print("❌ 找不到結果文件")

def main():
    parser = argparse.ArgumentParser(description="ACT Quick Start")
    parser.add_argument('--mode', type=str, choices=['quick', 'standard', 'full'], 
                       default='standard', help='運行模式')
    parser.add_argument('--skip_check', action='store_true', help='跳過環境檢查')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 環境檢查
    if not args.skip_check:
        if not check_environment():
            print("\n❌ 環境檢查失敗，請修復後重試")
            return
    
    # 選擇運行模式
    print(f"\n🎯 運行模式: {args.mode}")
    
    if args.mode == 'quick':
        print("⚡ 快速模式: 20個訓練樣本, 10個測試樣本")
        success = run_quick_test()
    elif args.mode == 'standard':
        print("🔬 標準模式: 100個訓練樣本, 50個測試樣本")
        success = run_standard_research()
    elif args.mode == 'full':
        print("🏆 完整模式: 200個訓練樣本, 100個測試樣本")
        success = run_full_research()
    
    # 顯示結果
    if success:
        show_results()
        
        print("\n🎉 ACT 研究完成!")
        print("\n📁 結果文件位置:")
        print("   - 摘要: complete_act_results/summary.txt")
        print("   - 詳細: complete_act_results/complete_results.pkl")
        print("   - 激活: complete_act_results/activations.pkl")
        print("   - 方向: complete_act_results/directions.pkl")
        
        print("\n🔄 下一步建議:")
        print("   1. 查看結果摘要")
        print("   2. 嘗試不同的 alpha 值")
        print("   3. 測試其他模型")
        print("   4. 增加樣本數量")
        
    else:
        print("\n❌ 運行失敗，請檢查錯誤信息")
        print("\n🔧 故障排除:")
        print("   1. 確保 conda activate ACT")
        print("   2. 檢查網絡連接")
        print("   3. 確保有足夠的 GPU 內存")
        print("   4. 嘗試減少樣本數量")

if __name__ == "__main__":
    main()
