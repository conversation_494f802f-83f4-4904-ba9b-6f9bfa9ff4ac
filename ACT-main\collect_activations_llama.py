import torch
from datasets import load_dataset
from tqdm import tqdm
import pandas as pd
import numpy as np
import pickle
import argparse
import sys
import os
from functools import partial
from transformers import AutoTokenizer, AutoModelForCausalLM

# Set Hugging Face token
os.environ['HUGGINGFACE_HUB_TOKEN'] = '*************************************'

def tokenized_tqa_all_llama(dataset, tokenizer):
    """Tokenization function for LLaMA"""
    tokenized_data = []
    for item in dataset:
        question = item['question']
        choices = item['mc1_targets']['choices']
        
        # Tokenize question and choices
        question_tokens = tokenizer.encode(question, add_special_tokens=False)
        choice_tokens = [tokenizer.encode(choice, add_special_tokens=False) for choice in choices]
        
        tokenized_data.append({
            'question': question,
            'question_tokens': question_tokens,
            'choices': choices,
            'choice_tokens': choice_tokens,
            'labels': item['mc1_targets']['labels']
        })
    
    return tokenized_data

def get_llama_activations_bau(model, tokenized_data, device):
    """Extract activations from LLaMA model"""
    all_activations = []
    all_labels = []
    
    with torch.no_grad():
        for item in tqdm(tokenized_data, desc="Extracting activations"):
            question_tokens = torch.tensor([item['question_tokens']], device=device)
            
            # For each choice, get the activation
            for choice_idx, choice_tokens in enumerate(item['choice_tokens']):
                # Combine question and choice
                full_tokens = torch.tensor([item['question_tokens'] + choice_tokens], device=device)
                
                # Get outputs with attention weights
                try:
                    choice_outputs = model(full_tokens, output_hidden_states=True, output_attentions=True)
                    choice_hidden_states = choice_outputs.hidden_states
                    
                    # Extract the last layer's hidden state (last token)
                    last_layer_hidden = choice_hidden_states[-1][0, -1, :].cpu().numpy()
                    all_activations.append(last_layer_hidden)
                    all_labels.append(item['labels'][choice_idx])
                except Exception as e:
                    print(f"Warning: Error processing choice {choice_idx}: {e}")
                    # Use zero vector as fallback
                    hidden_size = model.config.hidden_size
                    all_activations.append(np.zeros(hidden_size))
                    all_labels.append(item['labels'][choice_idx])
    
    return np.array(all_activations), np.array(all_labels)

def main(): 
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_name', type=str, default='llama2_7B')
    parser.add_argument('--device', type=int, default=0)
    parser.add_argument('--max_samples', type=int, default=None, help='Limit number of samples for testing')
    parser.add_argument('--use_4bit', action='store_true', help='Use 4-bit quantization to save memory')
    args = parser.parse_args()
    
    HF_NAMES = {
        'llama2_7B': 'meta-llama/Llama-2-7b-hf',
        'llama2_chat_7B': 'meta-llama/Llama-2-7b-chat-hf', 
        'llama2_13B': 'meta-llama/Llama-2-13b-hf',
        'huggyllama_7B': 'huggyllama/llama-7b',
        'open_llama_7B': 'openlm-research/open_llama_7b',
        'vicuna_7B': 'lmsys/vicuna-7b-v1.5',
        # Fallback options
        'gpt2': 'gpt2',
        'gpt2-medium': 'gpt2-medium'
    }
    
    print('Running LLaMA ACT:\n{}\n'.format(' '.join(sys.argv)))
    print(args)

    if args.model_name not in HF_NAMES:
        print(f"Error: Unknown model {args.model_name}")
        print(f"Available models: {list(HF_NAMES.keys())}")
        return

    MODEL = HF_NAMES[args.model_name]
    print(f"Loading model: {MODEL}")

    try:
        # Load tokenizer
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(MODEL, token=os.environ.get('HUGGINGFACE_HUB_TOKEN'))
        
        # Set pad token for LLaMA
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print("Loading model...")
        
        # Configure model loading based on your hardware
        model_kwargs = {
            'torch_dtype': torch.float16,
            'device_map': 'auto',
            'token': os.environ.get('HUGGINGFACE_HUB_TOKEN')
        }
        
        # Add quantization if requested
        if args.use_4bit:
            from transformers import BitsAndBytesConfig
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            model_kwargs['quantization_config'] = quantization_config
            print("Using 4-bit quantization")
        
        model = AutoModelForCausalLM.from_pretrained(MODEL, **model_kwargs)
        device = next(model.parameters()).device
        print(f"Model loaded on device: {device}")
        print(f"Model config: {model.config}")
        
    except Exception as e:
        print(f"Error loading LLaMA model: {e}")
        print("Falling back to GPT2 for testing...")
        MODEL = 'gpt2'
        tokenizer = AutoTokenizer.from_pretrained(MODEL)
        tokenizer.pad_token = tokenizer.eos_token
        model = AutoModelForCausalLM.from_pretrained(MODEL, torch_dtype=torch.float16)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        print(f"Fallback model loaded on device: {device}")

    # Load dataset
    print("Loading TruthfulQA dataset...")
    try:
        # Try to load real TruthfulQA dataset
        dataset = load_dataset('truthful_qa', 'multiple_choice')['validation']
        if args.max_samples:
            dataset = dataset.select(range(min(args.max_samples, len(dataset))))
        print(f"Dataset loaded: {len(dataset)} samples")
    except Exception as e:
        print(f"Error loading dataset: {e}")
        print("Creating a dummy dataset for testing...")
        # Create a simple test dataset
        dataset = [
            {
                'question': 'What is the capital of France?',
                'mc1_targets': {
                    'choices': ['Paris', 'London', 'Berlin', 'Madrid'],
                    'labels': [1, 0, 0, 0]
                }
            },
            {
                'question': 'What is 2+2?',
                'mc1_targets': {
                    'choices': ['3', '4', '5', '6'],
                    'labels': [0, 1, 0, 0]
                }
            },
            {
                'question': 'What color is the sky during a clear day?',
                'mc1_targets': {
                    'choices': ['Blue', 'Red', 'Green', 'Yellow'],
                    'labels': [1, 0, 0, 0]
                }
            }
        ]
        if args.max_samples:
            dataset = dataset[:args.max_samples]
        print(f"Using dummy dataset: {len(dataset)} samples")

    # Load reference CSV if available
    try:
        ref_df = pd.read_csv('./TruthfulQA/TruthfulQA.csv')
        print(f"Reference CSV loaded: {len(ref_df)} entries")
    except Exception as e:
        print(f"Warning: Could not load reference CSV: {e}")
        ref_df = None

    # Tokenize data
    print("Tokenizing data...")
    tokenized_data = tokenized_tqa_all_llama(dataset, tokenizer)
    print(f"Tokenized {len(tokenized_data)} samples")

    # Extract activations
    print("Extracting activations...")
    activations, labels = get_llama_activations_bau(model, tokenized_data, device)
    
    # Save activations and labels
    activations_file = f'./activations/{args.model_name}_head_wise.pkl'
    labels_file = f'./activations/{args.model_name}_labels.pkl'
    
    print(f"Saving activations to {activations_file}")
    with open(activations_file, 'wb') as f:
        pickle.dump(activations, f)
    
    print(f"Saving labels to {labels_file}")
    with open(labels_file, 'wb') as f:
        pickle.dump(labels, f)
    
    print(f"✓ Activations saved successfully!")
    print(f"  - Model: {args.model_name}")
    print(f"  - Activations shape: {activations.shape}")
    print(f"  - Labels shape: {labels.shape}")
    print(f"  - Activations file: {activations_file}")
    print(f"  - Labels file: {labels_file}")

if __name__ == "__main__":
    main()
