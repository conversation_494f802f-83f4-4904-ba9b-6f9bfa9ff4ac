#!/usr/bin/env python3
"""
ACT with REAL TruthfulQA data (790 questions!)
"""

import torch
import numpy as np
import argparse
import pickle as pkl
import sys
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm
import pandas as pd

# Set HF token
os.environ['HUGGINGFACE_HUB_TOKEN'] = '*************************************'

def load_real_truthfulqa(num_samples=50):
    """Load real TruthfulQA data"""
    
    try:
        # Try to load our preprocessed data first
        with open('./large_truthfulqa_test.pkl', 'rb') as f:
            data = pkl.load(f)
        print(f"✅ Loaded preprocessed data: {len(data)} questions")
        return data[:num_samples]
    
    except FileNotFoundError:
        print("📊 Loading from CSV...")
        df = pd.read_csv('./TruthfulQA/TruthfulQA.csv')
        
        test_data = []
        for idx, row in df.iterrows():
            if idx >= num_samples:
                break
                
            question = row['Question']
            best_answer = row['Best Answer']
            
            # Parse answers
            try:
                correct_answers = row['Correct Answers'].split('; ')
                incorrect_answers = row['Incorrect Answers'].split('; ')
            except:
                continue
            
            # Create multiple choice
            choices = [best_answer]
            for inc_ans in incorrect_answers[:3]:
                if inc_ans not in choices and len(inc_ans) < 100:  # Avoid very long answers
                    choices.append(inc_ans)
            
            while len(choices) < 4:
                choices.append("I don't know")
            
            labels = [1] + [0] * (len(choices) - 1)
            
            test_data.append({
                'question': question,
                'mc1_targets': {
                    'choices': choices[:4],
                    'labels': labels[:4]
                },
                'category': row['Category']
            })
        
        print(f"✅ Processed {len(test_data)} questions from CSV")
        return test_data

def evaluate_baseline_real(model, tokenizer, dataset):
    """Evaluate baseline on real data"""
    
    correct = 0
    total = 0
    category_results = {}
    
    for item in tqdm(dataset, desc="Evaluating baseline"):
        question = item['question']
        choices = item['mc1_targets']['choices']
        labels = item['mc1_targets']['labels']
        category = item.get('category', 'Unknown')
        
        correct_idx = labels.index(1) if 1 in labels else 0
        
        # Simple evaluation: score each choice
        choice_scores = []
        for choice in choices:
            prompt = f"Question: {question}\nAnswer: {choice}"
            
            inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=256)
            inputs = {k: v.to(model.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model(**inputs)
                # Simple scoring: average logit
                score = outputs.logits[0, -1, :].mean().item()
                choice_scores.append(score)
        
        predicted_idx = np.argmax(choice_scores)
        is_correct = predicted_idx == correct_idx
        
        if is_correct:
            correct += 1
        total += 1
        
        # Track by category
        if category not in category_results:
            category_results[category] = {'correct': 0, 'total': 0}
        category_results[category]['total'] += 1
        if is_correct:
            category_results[category]['correct'] += 1
    
    overall_accuracy = correct / total if total > 0 else 0
    
    return overall_accuracy, category_results

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_name', type=str, default='llama2_7B')
    parser.add_argument('--num_samples', type=int, default=50, help='Number of questions to test')
    parser.add_argument('--output_dir', type=str, default='./real_data_results')
    
    args = parser.parse_args()
    
    print("🦙 ACT with REAL TruthfulQA Data")
    print("=" * 50)
    print(f"Model: {args.model_name}")
    print(f"Samples: {args.num_samples}")
    print()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Model mapping
    HF_NAMES = {
        'llama2_7B': 'meta-llama/Llama-2-7b-hf',
        'llama2_chat_7B': 'meta-llama/Llama-2-7b-chat-hf',
    }
    
    # Load model
    print(f"Loading model: {args.model_name}")
    model_path = HF_NAMES[args.model_name]
    tokenizer = AutoTokenizer.from_pretrained(model_path, token=os.environ.get('HUGGINGFACE_HUB_TOKEN'))
    tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16,
        device_map='auto',
        token=os.environ.get('HUGGINGFACE_HUB_TOKEN')
    )
    
    # Load real data
    print("📊 Loading REAL TruthfulQA data...")
    dataset = load_real_truthfulqa(args.num_samples)
    
    print(f"📈 Dataset loaded: {len(dataset)} questions")
    
    # Show sample questions
    print("\n🔍 Sample questions:")
    for i, item in enumerate(dataset[:3]):
        print(f"{i+1}. {item['question']}")
        print(f"   Category: {item.get('category', 'Unknown')}")
        print(f"   Correct: {item['mc1_targets']['choices'][0]}")
    
    # Evaluate baseline
    print(f"\n🧪 Evaluating baseline performance...")
    overall_accuracy, category_results = evaluate_baseline_real(model, tokenizer, dataset)
    
    # Print results
    print(f"\n{'='*60}")
    print(f"REAL TRUTHFULQA RESULTS")
    print(f"{'='*60}")
    print(f"📊 Overall Accuracy: {overall_accuracy:.3f}")
    print(f"📈 Total Questions: {len(dataset)}")
    print()
    
    print("📋 Results by Category:")
    for category, results in sorted(category_results.items()):
        if results['total'] > 0:
            acc = results['correct'] / results['total']
            print(f"   {category}: {acc:.3f} ({results['correct']}/{results['total']})")
    
    # Save results
    results = {
        'model_name': args.model_name,
        'num_samples': args.num_samples,
        'overall_accuracy': overall_accuracy,
        'category_results': category_results,
        'dataset_size': len(dataset)
    }
    
    results_file = os.path.join(args.output_dir, f'{args.model_name}_real_data_results.pkl')
    with open(results_file, 'wb') as f:
        pkl.dump(results, f)
    
    summary_file = os.path.join(args.output_dir, f'{args.model_name}_real_data_summary.txt')
    with open(summary_file, 'w') as f:
        f.write(f"ACT with REAL TruthfulQA Data\n")
        f.write(f"Model: {args.model_name}\n")
        f.write(f"Questions: {len(dataset)}\n")
        f.write(f"Overall Accuracy: {overall_accuracy:.3f}\n\n")
        f.write(f"Category Results:\n")
        for category, results in sorted(category_results.items()):
            if results['total'] > 0:
                acc = results['correct'] / results['total']
                f.write(f"  {category}: {acc:.3f} ({results['correct']}/{results['total']})\n")
    
    print(f"\n💾 Results saved to {args.output_dir}")
    
    print(f"\n🎯 CONCLUSION:")
    print(f"   - This is using REAL TruthfulQA data ({len(dataset)} questions)")
    print(f"   - Original paper used 790 questions total")
    print(f"   - Our previous 3-question test was just a demo")
    print(f"   - With real data, we can see actual performance patterns!")

if __name__ == '__main__':
    main()
