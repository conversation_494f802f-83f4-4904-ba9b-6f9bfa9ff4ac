#!/usr/bin/env python3
"""
ACT with LLaMA - Complete pipeline with fallback options
"""

import argparse
import subprocess
import sys
import os
import time

def check_model_access(model_name):
    """Check if we can access the specified model"""
    print(f"🔍 Checking access to {model_name}...")
    
    # Set HF token
    os.environ['HUGGINGFACE_HUB_TOKEN'] = '*************************************'
    
    try:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name, token=os.environ.get('HUGGINGFACE_HUB_TOKEN'))
        print(f"✅ Successfully accessed {model_name}")
        return True
    except Exception as e:
        print(f"❌ Cannot access {model_name}: {str(e)[:100]}...")
        return False

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    print(f"Running: {command}")
    print()
    
    start_time = time.time()
    result = subprocess.run(command, shell=True, capture_output=False)
    end_time = time.time()
    
    if result.returncode == 0:
        print(f"\n✅ {description} completed successfully in {end_time - start_time:.1f}s")
        return True
    else:
        print(f"\n❌ {description} failed with return code {result.returncode}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Run ACT with LLaMA models")
    parser.add_argument('--model_name', type=str, default='auto', 
                       help='Model name (auto, llama2_7B, huggyllama_7B, gpt2, etc.)')
    parser.add_argument('--max_samples', type=int, default=5,
                       help='Maximum number of samples for testing')
    parser.add_argument('--alpha', type=float, default=2.0,
                       help='Steering strength for validation')
    parser.add_argument('--use_4bit', action='store_true',
                       help='Use 4-bit quantization to save memory')
    parser.add_argument('--force_gpt2', action='store_true',
                       help='Force use GPT2 instead of LLaMA')
    
    args = parser.parse_args()
    
    print("🦙 ACT with LLaMA - Complete Pipeline")
    print("="*60)
    print(f"Requested model: {args.model_name}")
    print(f"Max samples: {args.max_samples}")
    print(f"Alpha: {args.alpha}")
    print(f"4-bit quantization: {args.use_4bit}")
    print()
    
    # Model selection logic
    if args.force_gpt2:
        selected_model = 'gpt2'
        script_suffix = '_fixed'
        num_heads = 12
        print("🔄 Forced to use GPT2")
    elif args.model_name == 'auto':
        # Try to find the best available model
        print("🔍 Auto-detecting best available model...")
        
        candidate_models = [
            ('llama2_7B', 'meta-llama/Llama-2-7b-hf'),
            ('huggyllama_7B', 'huggyllama/llama-7b'),
            ('open_llama_7B', 'openlm-research/open_llama_7b'),
            ('gpt2', 'gpt2')
        ]
        
        selected_model = None
        for model_key, model_path in candidate_models:
            if check_model_access(model_path):
                selected_model = model_key
                break
        
        if selected_model is None:
            print("❌ No models accessible, falling back to GPT2")
            selected_model = 'gpt2'
        
        # Determine script type and parameters
        if 'llama' in selected_model.lower():
            script_suffix = '_llama'
            num_heads = 32  # LLaMA typically has 32 heads
        else:
            script_suffix = '_fixed'
            num_heads = 12  # GPT2 has 12 heads
            
    else:
        selected_model = args.model_name
        if 'llama' in selected_model.lower():
            script_suffix = '_llama'
            num_heads = 32
        else:
            script_suffix = '_fixed'
            num_heads = 12
    
    print(f"🎯 Selected model: {selected_model}")
    print(f"📝 Using scripts: *{script_suffix}.py")
    print(f"🧠 Number of heads: {num_heads}")
    print()
    
    # Check if conda environment is activated
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'ACT':
        print("⚠️  Warning: ACT conda environment may not be activated")
        print("   Please run: conda activate ACT")
        print()
    
    success_count = 0
    total_steps = 3
    
    # Step 1: Collect Activations
    if script_suffix == '_llama':
        cmd = f"python collect_activations_llama.py --model_name {selected_model} --max_samples {args.max_samples}"
        if args.use_4bit:
            cmd += " --use_4bit"
    else:
        cmd = f"python collect_activations_fixed.py --model_name {selected_model} --max_samples {args.max_samples}"
    
    if run_command(cmd, "Collecting Activations"):
        success_count += 1
    else:
        print("❌ Failed to collect activations. Trying fallback...")
        # Fallback to GPT2
        if selected_model != 'gpt2':
            print("🔄 Falling back to GPT2...")
            selected_model = 'gpt2'
            num_heads = 12
            cmd = f"python collect_activations_fixed.py --model_name gpt2 --max_samples {args.max_samples}"
            if run_command(cmd, "Collecting Activations (Fallback)"):
                success_count += 1
            else:
                print("❌ Even fallback failed. Stopping pipeline.")
                return
        else:
            print("❌ Fallback also failed. Stopping pipeline.")
            return
    
    # Step 2: Generate Directions
    if 'llama' in selected_model.lower() and script_suffix == '_llama':
        cmd = f"python generate_directions_fixed.py --model_name {selected_model} --num_heads {num_heads}"
    else:
        cmd = f"python generate_directions_fixed.py --model_name {selected_model} --num_heads {num_heads}"
    
    if run_command(cmd, "Generating Directions"):
        success_count += 1
    else:
        print("❌ Failed to generate directions. Stopping pipeline.")
        return
    
    # Step 3: Validate
    cmd = f"python validate_act_simplified.py --model_name {selected_model} --alpha {args.alpha}"
    if run_command(cmd, "Validating ACT"):
        success_count += 1
    else:
        print("❌ Failed to validate ACT.")
        return
    
    # Summary
    print(f"\n{'='*60}")
    print("🎉 ACT PIPELINE COMPLETE!")
    print(f"{'='*60}")
    print(f"✅ Successfully completed {success_count}/{total_steps} steps")
    print(f"🎯 Final model used: {selected_model}")
    print()
    print("📁 Generated Files:")
    print(f"   - Activations: ./activations/{selected_model}_head_wise.pkl")
    print(f"   - Labels: ./activations/{selected_model}_labels.pkl")
    print(f"   - Directions: ./directions/{selected_model}_directions.pkl")
    print(f"   - Results: ./validation/{selected_model}_alpha{args.alpha}_simplified/")
    print()
    
    # Show results
    try:
        summary_file = f"./validation/{selected_model}_alpha{args.alpha}_simplified/summary.txt"
        if os.path.exists(summary_file):
            print("📊 Results Summary:")
            with open(summary_file, 'r') as f:
                print(f.read())
    except Exception as e:
        print(f"Could not read results: {e}")
    
    print("🚀 Next Steps:")
    print("   1. Try different alpha values for optimization")
    print("   2. Increase sample size for more robust results")
    print("   3. Test with different models if available")
    print(f"   4. Re-run with: python run_act_with_llama.py --model_name {selected_model} --alpha 3.0")

if __name__ == "__main__":
    main()
