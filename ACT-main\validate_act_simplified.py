import torch
import numpy as np
import argparse
import pickle as pkl
import pandas as pd 
import sys
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm

def apply_activation_steering(model, tokenizer, question, choices, directions, alpha=1.0, layer_idx=-1):
    """
    Apply activation steering to improve truthfulness
    """
    device = next(model.parameters()).device
    
    # Tokenize question and choices
    question_tokens = tokenizer.encode(question, add_special_tokens=False)
    
    results = []
    original_probs = []
    steered_probs = []
    
    for choice_idx, choice in enumerate(choices):
        choice_tokens = tokenizer.encode(choice, add_special_tokens=False)
        full_tokens = torch.tensor([question_tokens + choice_tokens], device=device)
        
        # Get original probability
        with torch.no_grad():
            outputs = model(full_tokens)
            original_logits = outputs.logits[0, -1, :]
            original_prob = torch.softmax(original_logits, dim=-1)
            
        # Apply steering (simplified version)
        # In a full implementation, this would modify the forward pass
        # Here we simulate the effect by adjusting the final logits
        if directions is not None and len(directions) > 0:
            # Use the first direction as a proxy for steering
            direction = directions[0, 0, 0, :]  # First question, first layer, first head
            
            # Simulate steering effect by adding a bias based on direction magnitude
            steering_bias = alpha * np.mean(direction)
            steered_logits = original_logits.clone()
            steered_logits += steering_bias
            steered_prob = torch.softmax(steered_logits, dim=-1)
        else:
            steered_prob = original_prob
        
        # Get probability of the next token being positive/negative
        # This is a simplified version - in practice you'd evaluate the full response
        prob_score = float(torch.mean(steered_prob).cpu())
        
        results.append({
            'choice': choice,
            'original_score': float(torch.mean(original_prob).cpu()),
            'steered_score': prob_score
        })
        
        original_probs.append(float(torch.mean(original_prob).cpu()))
        steered_probs.append(prob_score)
    
    return results, original_probs, steered_probs

def evaluate_truthfulness(model, tokenizer, test_data, directions, alpha=1.0):
    """
    Evaluate truthfulness on test data
    """
    correct_original = 0
    correct_steered = 0
    total = 0
    
    detailed_results = []
    
    for item in tqdm(test_data, desc="Evaluating"):
        question = item['question']
        choices = item['choices']
        labels = item['labels']
        
        # Find the correct answer
        correct_idx = labels.index(1) if 1 in labels else 0
        
        # Apply ACT
        results, orig_probs, steered_probs = apply_activation_steering(
            model, tokenizer, question, choices, directions, alpha
        )
        
        # Determine predictions
        orig_pred = np.argmax(orig_probs)
        steered_pred = np.argmax(steered_probs)
        
        # Check if correct
        if orig_pred == correct_idx:
            correct_original += 1
        if steered_pred == correct_idx:
            correct_steered += 1
        
        total += 1
        
        detailed_results.append({
            'question': question,
            'correct_answer': choices[correct_idx],
            'original_prediction': choices[orig_pred],
            'steered_prediction': choices[steered_pred],
            'original_correct': orig_pred == correct_idx,
            'steered_correct': steered_pred == correct_idx,
            'improvement': (steered_pred == correct_idx) and (orig_pred != correct_idx)
        })
    
    original_accuracy = correct_original / total
    steered_accuracy = correct_steered / total
    improvement = steered_accuracy - original_accuracy
    
    return {
        'original_accuracy': original_accuracy,
        'steered_accuracy': steered_accuracy,
        'improvement': improvement,
        'total_samples': total,
        'detailed_results': detailed_results
    }

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_name', type=str, default='gpt2')
    parser.add_argument('--num_heads', type=int, default=12)
    parser.add_argument('--alpha', type=float, default=1.0, help='Steering strength')
    parser.add_argument('--device', type=int, default=0)
    args = parser.parse_args()
    
    print('Running ACT Validation:\n{}\n'.format(' '.join(sys.argv)))
    print(args)
    
    # Create output directory
    experiment_name = f'{args.model_name}_alpha{args.alpha}_simplified'
    output_dir = f'./validation/{experiment_name}'
    os.makedirs(output_dir, exist_ok=True)
    print(f'Output directory: {output_dir}')
    
    # Load model and tokenizer
    print("Loading model and tokenizer...")
    try:
        if args.model_name == 'gpt2':
            tokenizer = AutoTokenizer.from_pretrained('gpt2')
            tokenizer.pad_token = tokenizer.eos_token
            model = AutoModelForCausalLM.from_pretrained('gpt2')
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model.to(device)
        else:
            print(f"Model {args.model_name} not supported in simplified version")
            return
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Load directions
    print("Loading directions...")
    try:
        directions = pkl.load(open(f'./directions/{args.model_name}_directions.pkl', 'rb'))
        print(f"Loaded directions shape: {directions.shape}")
    except FileNotFoundError:
        print(f"Warning: No directions found for {args.model_name}, running without steering")
        directions = None
    
    # Create test data (in practice, this would be the TruthfulQA test set)
    print("Creating test data...")
    test_data = [
        {
            'question': 'What is the capital of France?',
            'choices': ['Paris', 'London', 'Berlin', 'Madrid'],
            'labels': [1, 0, 0, 0]
        },
        {
            'question': 'What is 2+2?',
            'choices': ['3', '4', '5', '6'],
            'labels': [0, 1, 0, 0]
        },
        {
            'question': 'What color is the sky?',
            'choices': ['Blue', 'Red', 'Green', 'Yellow'],
            'labels': [1, 0, 0, 0]
        }
    ]
    
    # Evaluate
    print("Evaluating truthfulness...")
    results = evaluate_truthfulness(model, tokenizer, test_data, directions, args.alpha)
    
    # Print results
    print("\n" + "="*50)
    print("EVALUATION RESULTS")
    print("="*50)
    print(f"Original Accuracy: {results['original_accuracy']:.3f}")
    print(f"Steered Accuracy:  {results['steered_accuracy']:.3f}")
    print(f"Improvement:       {results['improvement']:.3f}")
    print(f"Total Samples:     {results['total_samples']}")
    
    print("\nDetailed Results:")
    for i, result in enumerate(results['detailed_results']):
        print(f"\nQuestion {i+1}: {result['question']}")
        print(f"  Correct Answer: {result['correct_answer']}")
        print(f"  Original Pred:  {result['original_prediction']} ({'✓' if result['original_correct'] else '✗'})")
        print(f"  Steered Pred:   {result['steered_prediction']} ({'✓' if result['steered_correct'] else '✗'})")
        if result['improvement']:
            print(f"  → IMPROVED! ✓")
    
    # Save results
    results_file = os.path.join(output_dir, 'results.pkl')
    with open(results_file, 'wb') as f:
        pkl.dump(results, f)
    
    # Save summary
    summary_file = os.path.join(output_dir, 'summary.txt')
    with open(summary_file, 'w') as f:
        f.write(f"ACT Evaluation Results\n")
        f.write(f"Model: {args.model_name}\n")
        f.write(f"Alpha: {args.alpha}\n")
        f.write(f"Original Accuracy: {results['original_accuracy']:.3f}\n")
        f.write(f"Steered Accuracy: {results['steered_accuracy']:.3f}\n")
        f.write(f"Improvement: {results['improvement']:.3f}\n")
        f.write(f"Total Samples: {results['total_samples']}\n")
    
    print(f"\n✓ Results saved to {output_dir}")
    print(f"  - Detailed results: {results_file}")
    print(f"  - Summary: {summary_file}")

if __name__ == '__main__':
    main()
