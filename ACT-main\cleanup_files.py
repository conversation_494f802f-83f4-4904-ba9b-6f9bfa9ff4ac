#!/usr/bin/env python3
"""
清理不需要的文件，只保留核心文件
"""

import os
import shutil

def cleanup_act_files():
    """清理 ACT 專案文件"""
    
    print("🧹 ACT 文件清理工具")
    print("=" * 50)
    
    # 核心文件（必須保留）
    core_files = {
        # 原始專案文件
        'collect_activations.py',
        'generate_directions_q_wise.py', 
        'valid_2_fold.py',
        'utils.py',
        'utils_simple.py',
        'environment.yaml',
        
        # 主要入口點
        'run_act_with_llama.py',           # ⭐ 主要使用
        'complete_act_research.py',        # ⭐ 研究級實現
        
        # 修復版本（備用）
        'collect_activations_fixed.py',
        'generate_directions_fixed.py',
        'validate_act_llama.py',
        
        # 文檔
        'ACT_USAGE_GUIDE.md',
        'QUICK_REFERENCE.md',
        'ACT_SETUP_GUIDE.md',
        
        # 工具
        'activate_act.bat',
        'cleanup_files.py'
    }
    
    # 可選文件（可以刪除）
    optional_files = {
        'test_environment.py',
        'test_basic_functionality.py',
        'check_truthfulqa.py',
        'analyze_real_truthfulqa.py',
        'large_scale_act_test.py',
        'act_with_real_data.py',
        'setup_hf_auth.py',
        'act_research_implementation.py',
        'improved_act_implementation.py',
        'validate_act_simplified.py',
        'run_act_simplified.py',
        'run_act_complete.py',
        'large_truthfulqa_test.pkl'
    }
    
    # 檢查當前目錄的文件
    current_files = set(os.listdir('.'))
    
    print("📁 當前文件分析:")
    print(f"   核心文件: {len(core_files & current_files)} 個")
    print(f"   可選文件: {len(optional_files & current_files)} 個")
    print(f"   其他文件: {len(current_files - core_files - optional_files)} 個")
    
    # 顯示可選文件
    optional_present = optional_files & current_files
    if optional_present:
        print(f"\n🗑️ 可以刪除的文件 ({len(optional_present)} 個):")
        for file in sorted(optional_present):
            size = os.path.getsize(file) / 1024  # KB
            print(f"   - {file} ({size:.1f} KB)")
    
    # 詢問是否刪除
    if optional_present:
        print(f"\n❓ 是否刪除這些可選文件？")
        print("   這些文件是測試和開發用的，刪除後不影響核心功能")
        choice = input("   輸入 'yes' 確認刪除，其他任意鍵取消: ").lower().strip()
        
        if choice == 'yes':
            deleted_count = 0
            for file in optional_present:
                try:
                    os.remove(file)
                    print(f"   ✅ 已刪除: {file}")
                    deleted_count += 1
                except Exception as e:
                    print(f"   ❌ 刪除失敗: {file} - {e}")
            
            print(f"\n🎉 清理完成！刪除了 {deleted_count} 個文件")
        else:
            print("\n🚫 取消清理")
    else:
        print("\n✨ 沒有可選文件需要清理")
    
    # 顯示保留的核心文件
    core_present = core_files & current_files
    print(f"\n📋 保留的核心文件 ({len(core_present)} 個):")
    
    categories = {
        '主要入口': ['run_act_with_llama.py', 'complete_act_research.py'],
        '原始專案': ['collect_activations.py', 'generate_directions_q_wise.py', 'valid_2_fold.py'],
        '修復版本': ['collect_activations_fixed.py', 'generate_directions_fixed.py', 'validate_act_llama.py'],
        '文檔指南': ['ACT_USAGE_GUIDE.md', 'QUICK_REFERENCE.md', 'ACT_SETUP_GUIDE.md'],
        '工具腳本': ['activate_act.bat', 'cleanup_files.py'],
        '其他': []
    }
    
    for category, files in categories.items():
        category_files = [f for f in files if f in core_present]
        if category_files:
            print(f"\n   {category}:")
            for file in category_files:
                print(f"     - {file}")
    
    # 其他核心文件
    other_core = core_present - set(sum(categories.values(), []))
    if other_core:
        print(f"\n   其他:")
        for file in sorted(other_core):
            print(f"     - {file}")
    
    print(f"\n📖 使用建議:")
    print(f"   - 快速開始: python run_act_with_llama.py")
    print(f"   - 完整研究: python complete_act_research.py") 
    print(f"   - 查看指南: 閱讀 ACT_USAGE_GUIDE.md")

def show_file_usage():
    """顯示文件使用說明"""
    
    usage_map = {
        'run_act_with_llama.py': '🌟 主要入口點 - 一鍵運行完整 ACT 流程',
        'complete_act_research.py': '🔬 研究級實現 - 使用 790 個真實問題',
        'collect_activations_fixed.py': '🔧 步驟1 - 收集激活值（修復版）',
        'generate_directions_fixed.py': '🔧 步驟2 - 生成方向向量（修復版）',
        'validate_act_llama.py': '🔧 步驟3 - 驗證評估（LLaMA版）',
        'ACT_USAGE_GUIDE.md': '📚 詳細使用指南',
        'QUICK_REFERENCE.md': '⚡ 快速參考卡',
        'activate_act.bat': '🚀 Windows 環境激活腳本'
    }
    
    print("\n📋 核心文件使用說明:")
    for file, description in usage_map.items():
        if os.path.exists(file):
            print(f"   {description}")
            print(f"     文件: {file}")

if __name__ == "__main__":
    cleanup_act_files()
    show_file_usage()
    
    print(f"\n🎯 下一步:")
    print(f"   1. 閱讀 ACT_USAGE_GUIDE.md 了解詳細用法")
    print(f"   2. 運行 python run_act_with_llama.py 開始體驗")
    print(f"   3. 查看 QUICK_REFERENCE.md 獲取快速參考")
