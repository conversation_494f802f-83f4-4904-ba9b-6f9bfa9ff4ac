#!/usr/bin/env python3
"""
Analyze the real TruthfulQA dataset
"""

import pandas as pd
import numpy as np

def analyze_truthfulqa():
    """Analyze the real TruthfulQA dataset"""
    
    print("📊 Analyzing REAL TruthfulQA Dataset")
    print("=" * 50)
    
    # Load the CSV
    df = pd.read_csv('./TruthfulQA/TruthfulQA.csv')
    
    print(f"📈 Total questions: {len(df)}")
    print(f"📂 Categories: {list(df['Category'].unique())}")
    print(f"🎯 Types: {list(df['Type'].unique())}")
    
    print("\n📋 Category breakdown:")
    category_counts = df['Category'].value_counts()
    for category, count in category_counts.items():
        print(f"   {category}: {count} questions")
    
    print("\n🔍 Sample questions by category:")
    for category in df['Category'].unique()[:5]:  # Show first 5 categories
        sample = df[df['Category'] == category].iloc[0]
        print(f"\n📌 {category}:")
        print(f"   Q: {sample['Question']}")
        print(f"   A: {sample['Best Answer']}")
    
    print(f"\n🎉 CONCLUSION:")
    print(f"   - The original ACT paper used {len(df)} questions!")
    print(f"   - We only used 3 questions due to dataset access issues")
    print(f"   - This explains why our results were limited")
    print(f"   - With {len(df)} questions, we could get much better results!")

def create_large_scale_test():
    """Create a large-scale test using the real data"""
    
    df = pd.read_csv('./TruthfulQA/TruthfulQA.csv')
    
    # Convert to our format
    test_data = []
    
    for idx, row in df.iterrows():
        if idx >= 50:  # Limit to first 50 for testing
            break
            
        question = row['Question']
        best_answer = row['Best Answer']
        
        # Parse correct and incorrect answers
        correct_answers = row['Correct Answers'].split('; ')
        incorrect_answers = row['Incorrect Answers'].split('; ')
        
        # Create multiple choice format
        choices = [best_answer]
        
        # Add some incorrect answers
        for inc_ans in incorrect_answers[:3]:  # Take first 3 incorrect
            if inc_ans not in choices:
                choices.append(inc_ans)
        
        # Ensure we have 4 choices
        while len(choices) < 4:
            choices.append("I don't know")
        
        # Create labels (first choice is correct)
        labels = [1] + [0] * (len(choices) - 1)
        
        test_data.append({
            'question': question,
            'mc1_targets': {
                'choices': choices[:4],  # Limit to 4 choices
                'labels': labels[:4]
            },
            'category': row['Category'],
            'type': row['Type']
        })
    
    print(f"\n🚀 Created test dataset with {len(test_data)} questions")
    
    # Save for later use
    import pickle
    with open('./large_truthfulqa_test.pkl', 'wb') as f:
        pickle.dump(test_data, f)
    
    print(f"💾 Saved to large_truthfulqa_test.pkl")
    
    return test_data

if __name__ == "__main__":
    analyze_truthfulqa()
    test_data = create_large_scale_test()
    
    print(f"\n🎯 Now you can run ACT with {len(test_data)} REAL questions!")
    print(f"   This is much closer to the original paper's scale!")
