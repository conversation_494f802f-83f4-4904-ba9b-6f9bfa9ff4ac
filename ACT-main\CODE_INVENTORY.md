# ACT 代碼文件清單與使用指南

## 📊 文件重要性評級

### ⭐⭐⭐⭐⭐ 核心文件（必須掌握）

#### 1. `quick_start.py` 
**一鍵運行腳本**
```bash
# 最簡單的使用方式
python quick_start.py --mode quick      # 5分鐘快速測試
python quick_start.py --mode standard   # 15分鐘標準研究
python quick_start.py --mode full       # 45分鐘完整研究
```

#### 2. `complete_act_research.py`
**完整研究實現**
```bash
# 研究級別的完整實現
python complete_act_research.py --model_name llama2_7B
```

### ⭐⭐⭐⭐ 重要文件（推薦使用）

#### 3. `run_act_with_llama.py`
**智能模型選擇**
```bash
# 自動選擇最佳可用模型
python run_act_with_llama.py --model_name auto
```

#### 4. `ACT_USER_GUIDE.md`
**完整使用指南**
- 詳細的使用教程
- 場景化的使用方法
- 故障排除指南

### ⭐⭐⭐ 功能文件（按需使用）

#### 5. `collect_activations_llama.py`
**激活提取**
```bash
# 單獨提取激活值
python collect_activations_llama.py --model_name llama2_7B
```

#### 6. `validate_act_llama.py`
**驗證評估**
```bash
# 使用已有方向向量進行驗證
python validate_act_llama.py --model_name llama2_7B --alpha 2.0
```

#### 7. `act_with_real_data.py`
**真實數據測試**
```bash
# 測試模型在真實數據上的基線性能
python act_with_real_data.py --model_name llama2_7B
```

### ⭐⭐ 分析文件（了解即可）

#### 8. `analyze_real_truthfulqa.py`
**數據集分析**
```bash
# 分析 TruthfulQA 數據集
python analyze_real_truthfulqa.py
```

#### 9. `large_scale_act_test.py`
**大規模測試**
```bash
# 大規模基線測試
python large_scale_act_test.py --num_samples 50
```

#### 10. `improved_act_implementation.py`
**改進版實現**
```bash
# 改進的 ACT 實現
python improved_act_implementation.py --retrain_directions
```

### ⭐ 工具文件（輔助功能）

#### 11-15. 測試和設置文件
- `test_environment.py` - 環境測試
- `test_basic_functionality.py` - 基本功能測試
- `setup_hf_auth.py` - 認證設置
- `check_truthfulqa.py` - 數據集檢查
- `activate_act.bat` - Windows 激活腳本

#### 16-20. 早期版本文件（已被更好版本替代）
- `collect_activations_fixed.py` - 被 `collect_activations_llama.py` 替代
- `generate_directions_fixed.py` - 被 `complete_act_research.py` 包含
- `validate_act_simplified.py` - 被 `validate_act_llama.py` 替代
- `run_act_complete.py` - 被 `run_act_with_llama.py` 替代
- `run_act_simplified.py` - 被 `quick_start.py` 替代

## 🎯 推薦使用順序

### 新手用戶
```bash
# 1. 快速開始（5分鐘）
python quick_start.py --mode quick

# 2. 標準研究（15分鐘）
python quick_start.py --mode standard

# 3. 查看結果
cat complete_act_results/summary.txt
```

### 研究用戶
```bash
# 1. 了解數據
python analyze_real_truthfulqa.py

# 2. 完整研究
python complete_act_research.py --train_samples 200 --test_samples 100

# 3. 自定義實驗
python complete_act_research.py --alpha_values 0.0 0.5 1.0 2.0 5.0
```

### 開發用戶
```bash
# 1. 閱讀核心代碼
# complete_act_research.py

# 2. 理解組件
# collect_activations_llama.py
# validate_act_llama.py

# 3. 自定義開發
# 基於現有代碼進行修改
```

## 📁 文件組織結構

```
ACT-main/
├── 🚀 核心運行文件
│   ├── quick_start.py              ⭐⭐⭐⭐⭐ 一鍵運行
│   ├── complete_act_research.py    ⭐⭐⭐⭐⭐ 完整研究
│   └── run_act_with_llama.py       ⭐⭐⭐⭐ 智能選擇
│
├── 🔧 功能組件
│   ├── collect_activations_llama.py ⭐⭐⭐ 激活提取
│   ├── validate_act_llama.py        ⭐⭐⭐ 驗證評估
│   └── act_with_real_data.py        ⭐⭐⭐ 真實數據
│
├── 📊 分析工具
│   ├── analyze_real_truthfulqa.py   ⭐⭐ 數據分析
│   ├── large_scale_act_test.py      ⭐⭐ 大規模測試
│   └── improved_act_implementation.py ⭐⭐ 改進版本
│
├── 🛠️ 輔助工具
│   ├── test_environment.py          ⭐ 環境測試
│   ├── setup_hf_auth.py             ⭐ 認證設置
│   └── activate_act.bat             ⭐ 激活腳本
│
├── 📚 文檔指南
│   ├── ACT_USER_GUIDE.md           ⭐⭐⭐⭐ 使用指南
│   ├── CODE_INVENTORY.md           ⭐⭐⭐ 文件清單
│   └── ACT_SETUP_GUIDE.md          ⭐⭐ 設置指南
│
└── 📦 早期版本（可忽略）
    ├── collect_activations_fixed.py
    ├── generate_directions_fixed.py
    ├── validate_act_simplified.py
    └── run_act_complete.py
```

## 🎯 使用建議

### 第一次使用
1. **閱讀**: `ACT_USER_GUIDE.md`
2. **運行**: `python quick_start.py --mode quick`
3. **查看**: `complete_act_results/summary.txt`

### 日常研究
1. **標準**: `python quick_start.py --mode standard`
2. **自定義**: `python complete_act_research.py` + 參數
3. **分析**: 查看生成的結果文件

### 深度開發
1. **理解**: 閱讀 `complete_act_research.py` 源碼
2. **修改**: 基於需求修改參數和邏輯
3. **擴展**: 使用組件文件構建新功能

## 🔄 文件依賴關係

```
quick_start.py
    └── complete_act_research.py
            ├── CompleteTruthfulQADataset
            ├── ACTActivationExtractor
            ├── ACTDirectionComputer
            └── ACTInferenceEngine

run_act_with_llama.py
    ├── collect_activations_llama.py
    ├── generate_directions_fixed.py
    └── validate_act_llama.py
```

## 💡 使用技巧

### 快速測試
```bash
# 最快的驗證方式
python quick_start.py --mode quick
```

### 參數調優
```bash
# 測試不同 alpha 值
python complete_act_research.py --alpha_values 0.0 0.1 0.5 1.0 2.0 5.0
```

### 大規模實驗
```bash
# 使用更多數據
python complete_act_research.py --train_samples 300 --test_samples 150
```

### 結果分析
```python
# Python 中分析結果
import pickle
with open('complete_act_results/complete_results.pkl', 'rb') as f:
    results = pickle.load(f)
print(f"Best alpha: {results['best_alpha']}")
print(f"Improvement: {results['improvement']:.3f}")
```

## 🎯 總結

**最重要的 3 個文件**:
1. `quick_start.py` - 一鍵運行
2. `complete_act_research.py` - 完整研究
3. `ACT_USER_GUIDE.md` - 使用指南

**最常用的命令**:
```bash
python quick_start.py --mode standard
```

這一個命令就能運行完整的 ACT 研究！
