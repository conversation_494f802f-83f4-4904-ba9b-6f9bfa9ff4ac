import argparse
import sys
import numpy as np
import pickle as pkl
from einops import rearrange
from datasets import load_dataset

def get_separated_activations_fixed(labels, head_wise_activations, dataset_info=None):
    """
    Fixed version of get_separated_activations that works with our data format
    """
    if dataset_info is None:
        # Create dummy dataset info for our test data
        dataset_info = [
            {'mc1_targets': {'labels': [1, 0, 0, 0]}},  # 4 choices for question 1
            {'mc1_targets': {'labels': [0, 1, 0, 0]}}   # 4 choices for question 2
        ]
    
    actual_labels = []
    for item in dataset_info:
        if 'mc1_targets' in item:
            actual_labels.append(item['mc1_targets']['labels'])
        elif 'mc2_targets' in item:
            actual_labels.append(item['mc2_targets']['labels'])
        else:
            # Fallback
            actual_labels.append([1, 0, 0, 0])
    
    idxs_to_split_at = np.cumsum([len(x) for x in actual_labels])
    
    labels = list(labels)
    separated_labels = []
    for i in range(len(idxs_to_split_at)):
        if i == 0:
            separated_labels.append(labels[:idxs_to_split_at[i]])
        else:
            separated_labels.append(labels[idxs_to_split_at[i-1]:idxs_to_split_at[i]])
    
    # Split activations by question
    separated_head_wise_activations = []
    start_idx = 0
    for end_idx in idxs_to_split_at:
        separated_head_wise_activations.append(head_wise_activations[start_idx:end_idx])
        start_idx = end_idx
    
    return separated_head_wise_activations, separated_labels, idxs_to_split_at

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_name', type=str, default='gpt2')
    parser.add_argument('--num_heads', type=int, default=12, help='Number of attention heads (12 for GPT2, 32 for LLaMA)')
    args = parser.parse_args()
    
    print('Running:\n{}\n'.format(' '.join(sys.argv)))
    print(args)

    # Load activations and labels
    try:
        head_wise_activations = pkl.load(open(f'./activations/{args.model_name}_head_wise.pkl', 'rb'))
        labels = pkl.load(open(f'./activations/{args.model_name}_labels.pkl', 'rb'))
        print(f"Loaded activations: {head_wise_activations.shape}")
        print(f"Loaded labels: {labels.shape}")
    except FileNotFoundError as e:
        print(f"Error: Could not find activation files for {args.model_name}")
        print(f"Make sure you have run collect_activations_fixed.py first")
        return

    # Reshape activations to head-wise format based on model type
    if len(head_wise_activations.shape) == 2:
        # Shape is (batch, hidden_size) - need to add layer dimension and split into heads
        batch_size, hidden_size = head_wise_activations.shape
        head_dim = hidden_size // args.num_heads
        print(f"Reshaping activations to head-wise format: {args.num_heads} heads, {head_dim} dims each")

        # Add a layer dimension and reshape to (batch, 1, num_heads, head_dim)
        head_wise_activations = head_wise_activations.reshape(batch_size, 1, args.num_heads, head_dim)
    elif len(head_wise_activations.shape) == 3:
        # Shape is (batch, layers, hidden_size) - split into heads
        head_wise_activations = rearrange(head_wise_activations, 'b l (h d) -> b l h d', h=args.num_heads)
    else:
        # Already in correct format
        pass

    print(f"Head-wise activations shape: {head_wise_activations.shape}")

    # Create dataset info for separation
    # For our dummy data, we know we have 2 questions with 4 choices each
    dataset_info = [
        {'mc1_targets': {'labels': [1, 0, 0, 0]}},  # Question 1: Paris is correct
        {'mc1_targets': {'labels': [0, 1, 0, 0]}}   # Question 2: 4 is correct
    ]

    # Separate activations by question
    separated_head_wise_activations, separated_labels, idxs_to_split_at = get_separated_activations_fixed(
        labels, head_wise_activations, dataset_info
    )
    
    print(f"Separated into {len(separated_head_wise_activations)} questions")
    for i, (acts, labs) in enumerate(zip(separated_head_wise_activations, separated_labels)):
        print(f"Question {i+1}: {acts.shape} activations, labels: {labs}")

    # Generate directions for each question
    # Direction = mean(correct_activations) - mean(incorrect_activations)
    head_wise_activation_directions = []
    
    for question_activations, question_labels in zip(separated_head_wise_activations, separated_labels):
        question_labels = np.array(question_labels)
        
        # Get correct and incorrect activations
        correct_mask = question_labels == 1
        incorrect_mask = question_labels == 0
        
        if np.sum(correct_mask) > 0 and np.sum(incorrect_mask) > 0:
            correct_activations = question_activations[correct_mask]
            incorrect_activations = question_activations[incorrect_mask]
            
            # Calculate direction: correct - incorrect
            direction = correct_activations.mean(axis=0) - incorrect_activations.mean(axis=0)
            head_wise_activation_directions.append(direction)
        else:
            print(f"Warning: Question has no correct or incorrect answers, skipping")
            # Add zero direction as placeholder
            direction = np.zeros_like(question_activations[0])
            head_wise_activation_directions.append(direction)
    
    head_wise_activation_directions = np.array(head_wise_activation_directions)
    print(f"Generated directions shape: {head_wise_activation_directions.shape}")
    
    # Save directions
    output_file = f'./directions/{args.model_name}_directions.pkl'
    pkl.dump(head_wise_activation_directions, open(output_file, 'wb'))
    
    print(f"✓ Directions saved successfully!")
    print(f"  - Model: {args.model_name}")
    print(f"  - Directions shape: {head_wise_activation_directions.shape}")
    print(f"  - Output: {output_file}")

if __name__ == '__main__':
    main()
