#!/usr/bin/env python3
"""
Setup Hugging Face authentication for LLaMA models
"""

import os
from huggingface_hub import login

def setup_huggingface_auth():
    """Setup Hugging Face authentication"""
    
    # Your token
    hf_token = "*************************************"
    
    print("🔐 Setting up Hugging Face authentication...")
    
    try:
        # Login to Hugging Face
        login(token=hf_token, add_to_git_credential=True)
        print("✅ Successfully authenticated with Hugging Face!")
        
        # Set environment variable for future use
        os.environ['HUGGINGFACE_HUB_TOKEN'] = hf_token
        
        # Test access to a LLaMA model
        from transformers import AutoTokenizer
        
        print("🧪 Testing access to LLaMA models...")
        
        # Try different LLaMA models in order of preference
        test_models = [
            "meta-llama/Llama-2-7b-hf",
            "huggyllama/llama-7b", 
            "openlm-research/open_llama_7b"
        ]
        
        accessible_model = None
        for model_name in test_models:
            try:
                print(f"   Testing {model_name}...")
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                accessible_model = model_name
                print(f"✅ Successfully accessed {model_name}")
                break
            except Exception as e:
                print(f"❌ Cannot access {model_name}: {str(e)[:100]}...")
                continue
        
        if accessible_model:
            print(f"\n🎉 Recommended model: {accessible_model}")
            return accessible_model
        else:
            print("\n⚠️ No LLaMA models accessible. Will use fallback options.")
            return None
            
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return None

if __name__ == "__main__":
    model = setup_huggingface_auth()
    if model:
        print(f"\n✅ Setup complete! You can now use: {model}")
    else:
        print("\n⚠️ Setup incomplete. Check your token and model access.")
