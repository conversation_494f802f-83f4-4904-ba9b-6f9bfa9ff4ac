# ACT 專案使用指南

## 📋 概述

這個專案實現了完整的 ACT (Activation Steering) 研究，從簡單演示到研究級實現。以下是所有文件的用途和使用方法。

## 🗂️ 文件結構說明

### 📁 **核心文件（原始專案）**
```
ACT-main/
├── collect_activations.py      # 原始激活收集腳本
├── generate_directions_q_wise.py # 原始方向生成腳本  
├── valid_2_fold.py             # 原始驗證腳本
├── utils.py                    # 原始工具函數
├── utils_simple.py             # 簡化工具函數
└── llama/                      # LLaMA 模組（已修復兼容性）
```

### 📁 **我創建的文件**

#### 🎯 **快速開始文件**
```
run_act_with_llama.py           # ⭐ 主要入口點 - 智能選擇模型並運行完整流程
activate_act.bat                # Windows 批處理文件 - 快速激活環境
```

#### 🔧 **修復版本文件**
```
collect_activations_fixed.py    # 修復版激活收集（支援現代 transformers）
generate_directions_fixed.py    # 修復版方向生成
validate_act_simplified.py      # 簡化版驗證
validate_act_llama.py          # LLaMA 專用驗證
```

#### 🔬 **研究級實現**
```
complete_act_research.py        # ⭐ 完整研究實現 - 使用 790 個真實問題
act_research_implementation.py  # 研究級實現（帶 Hook）
improved_act_implementation.py  # 改進版實現
```

#### 📊 **測試和分析文件**
```
test_environment.py            # 環境測試
test_basic_functionality.py    # 基本功能測試
check_truthfulqa.py           # 檢查 TruthfulQA 數據集
analyze_real_truthfulqa.py    # 分析真實數據
large_scale_act_test.py       # 大規模測試
act_with_real_data.py         # 使用真實數據的測試
```

#### 📚 **文檔和設置**
```
ACT_SETUP_GUIDE.md            # 環境設置指南
ACT_USAGE_GUIDE.md            # 本文件 - 使用指南
setup_hf_auth.py              # Hugging Face 認證設置
```

## 🚀 **使用方法**

### 1️⃣ **快速開始（推薦）**

```bash
# 激活環境
conda activate ACT

# 運行完整流程（自動選擇最佳模型）
python run_act_with_llama.py --model_name auto --max_samples 10
```

### 2️⃣ **研究級完整實現（最佳）**

```bash
# 運行完整的 790 問題研究
python complete_act_research.py --model_name llama2_7B --train_samples 100 --test_samples 50
```

### 3️⃣ **分步運行（學習用）**

```bash
# 步驟 1: 收集激活
python collect_activations_fixed.py --model_name llama2_7B --max_samples 10

# 步驟 2: 生成方向
python generate_directions_fixed.py --model_name llama2_7B --num_heads 32

# 步驟 3: 驗證
python validate_act_llama.py --model_name llama2_7B --alpha 2.0
```

## 📊 **文件選擇指南**

### 🎯 **根據需求選擇**

| 需求 | 推薦文件 | 說明 |
|------|----------|------|
| **快速體驗** | `run_act_with_llama.py` | 一鍵運行，自動選擇模型 |
| **完整研究** | `complete_act_research.py` | 790 問題，研究級實現 |
| **學習理解** | `collect_activations_fixed.py` 等 | 分步運行，理解每個步驟 |
| **測試環境** | `test_environment.py` | 檢查環境是否正確設置 |
| **分析數據** | `analyze_real_truthfulqa.py` | 了解 TruthfulQA 數據集 |

### 🔧 **根據模型選擇**

| 模型類型 | 推薦文件 | 說明 |
|----------|----------|------|
| **LLaMA-2** | `run_act_with_llama.py` | 自動處理 LLaMA 模型 |
| **GPT-2** | `collect_activations_fixed.py` | 適用於較小模型 |
| **自動選擇** | `run_act_with_llama.py --model_name auto` | 智能選擇最佳可用模型 |

## 🎛️ **常用參數說明**

### 通用參數
```bash
--model_name        # 模型名稱 (llama2_7B, gpt2, auto)
--max_samples       # 最大樣本數 (10, 50, 100)
--alpha             # 引導強度 (1.0, 2.0, 5.0)
--num_heads         # 注意力頭數 (12 for GPT2, 32 for LLaMA)
```

### 研究級參數
```bash
--train_samples     # 訓練樣本數 (100, 200)
--test_samples      # 測試樣本數 (50, 100)
--alpha_values      # 多個 alpha 值測試
--save_activations  # 保存激活值
```

## 📈 **結果解讀**

### 輸出文件位置
```
activations/        # 激活值文件
directions/         # 方向向量文件
validation/         # 驗證結果
complete_act_results/  # 完整研究結果
```

### 關鍵指標
- **Baseline Accuracy**: 無引導的原始準確率
- **Steered Accuracy**: 引導後的準確率
- **Improvement**: 改進幅度
- **Best Alpha**: 最佳引導強度

## 🔍 **故障排除**

### 常見問題

1. **模型無法訪問**
   ```bash
   # 使用自動選擇
   python run_act_with_llama.py --model_name auto
   ```

2. **內存不足**
   ```bash
   # 減少樣本數
   python complete_act_research.py --train_samples 50 --test_samples 25
   ```

3. **環境問題**
   ```bash
   # 測試環境
   python test_environment.py
   ```

## 🎯 **推薦使用流程**

### 新手流程
1. **環境測試**: `python test_environment.py`
2. **快速體驗**: `python run_act_with_llama.py --max_samples 5`
3. **分析結果**: 查看 `validation/` 目錄

### 研究流程
1. **數據分析**: `python analyze_real_truthfulqa.py`
2. **完整研究**: `python complete_act_research.py --train_samples 200`
3. **結果分析**: 查看 `complete_act_results/` 目錄

### 開發流程
1. **分步測試**: 依次運行 `collect_activations_fixed.py` 等
2. **參數調優**: 測試不同的 alpha 值
3. **自定義實現**: 基於 `complete_act_research.py` 修改

## 📚 **學習建議**

### 理解 ACT 原理
1. 閱讀 `complete_act_research.py` - 最完整的實現
2. 運行 `analyze_real_truthfulqa.py` - 理解數據
3. 測試不同參數 - 理解影響

### 擴展研究
1. 修改 `complete_act_research.py` 中的方向計算方法
2. 嘗試不同的激活層
3. 實現更複雜的評估指標

## 🎉 **總結**

- **快速開始**: 使用 `run_act_with_llama.py`
- **完整研究**: 使用 `complete_act_research.py`
- **學習理解**: 分步運行各個文件
- **問題排除**: 使用測試文件

所有文件都已經過測試，可以直接使用。選擇適合您需求的文件開始探索 ACT 的神奇世界！
