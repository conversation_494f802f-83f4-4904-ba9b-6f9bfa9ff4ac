#!/usr/bin/env python3
"""
Check TruthfulQA dataset availability and size
"""

from datasets import load_dataset
import sys

def check_truthfulqa():
    """Check different configurations of TruthfulQA"""
    
    configs_to_try = [
        'generation',
        'multiple_choice', 
        'mc1',
        'mc2'
    ]
    
    for config in configs_to_try:
        try:
            print(f"Trying config: {config}")
            dataset = load_dataset('truthful_qa', config)
            
            print(f"✅ Success! Config: {config}")
            print(f"   Splits: {list(dataset.keys())}")
            
            if 'validation' in dataset:
                val_size = len(dataset['validation'])
                print(f"   Validation size: {val_size}")
                
                # Show first example
                if val_size > 0:
                    first_example = dataset['validation'][0]
                    print(f"   First question: {first_example.get('question', 'N/A')}")
                    print(f"   Keys: {list(first_example.keys())}")
            
            return dataset, config
            
        except Exception as e:
            print(f"❌ Failed config {config}: {str(e)[:100]}...")
            continue
    
    print("❌ No TruthfulQA config worked")
    return None, None

if __name__ == "__main__":
    dataset, config = check_truthfulqa()
    
    if dataset:
        print(f"\n🎉 Successfully loaded TruthfulQA with config: {config}")
        print(f"📊 This is the REAL dataset size that the paper used!")
    else:
        print(f"\n😞 Could not access TruthfulQA dataset")
        print(f"💡 This is why we had to use only 3 fallback questions")
