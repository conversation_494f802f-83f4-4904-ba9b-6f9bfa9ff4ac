#!/usr/bin/env python3
"""
Test script to verify the ACT environment setup
"""

def test_imports():
    """Test if all required packages can be imported"""
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA device count: {torch.cuda.device_count()}")
            print(f"✓ Current CUDA device: {torch.cuda.current_device()}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False

    try:
        import transformers
        print(f"✓ Transformers version: {transformers.__version__}")
    except ImportError as e:
        print(f"✗ Transformers import failed: {e}")
        return False

    try:
        import datasets
        print(f"✓ Datasets library available")
    except ImportError as e:
        print(f"✗ Datasets import failed: {e}")
        return False

    try:
        import pandas as pd
        print(f"✓ Pandas version: {pd.__version__}")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False

    try:
        import numpy as np
        print(f"✓ NumPy version: {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False

    try:
        import llama
        print(f"✓ LLaMA module available")
    except ImportError as e:
        print(f"✗ LLaMA import failed: {e}")
        return False

    try:
        from utils_simple import tokenized_tqa_all, get_llama_activations_bau
        print(f"✓ Utils module available")
    except ImportError as e:
        print(f"✗ Utils import failed: {e}")
        return False

    return True

def test_directories():
    """Test if required directories exist"""
    import os
    
    required_dirs = ['activations', 'directions', 'validation', 'TruthfulQA']
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✓ Directory '{dir_name}' exists")
        else:
            print(f"✗ Directory '{dir_name}' missing")
            return False
    
    return True

def main():
    print("=== ACT Environment Test ===\n")
    
    print("Testing package imports...")
    imports_ok = test_imports()
    
    print("\nTesting directory structure...")
    dirs_ok = test_directories()
    
    print(f"\n=== Test Results ===")
    if imports_ok and dirs_ok:
        print("✓ Environment setup is complete and ready!")
        return True
    else:
        print("✗ Environment setup has issues that need to be resolved.")
        return False

if __name__ == "__main__":
    main()
